{"name": "theme-toggle", "type": "registry:ui", "description": "A comprehensive dark mode toggle component with theme provider and multiple toggle variants.", "dependencies": ["next-themes", "lucide-react", "@radix-ui/react-dropdown-menu"], "devDependencies": [], "registryDependencies": ["button", "dropdown-menu"], "files": [{"name": "theme-provider.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}"}, {"name": "theme-toggle.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\n\nexport function ThemeToggle() {\n  const { setTheme, theme } = useTheme()\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"sm\"\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\n      className=\"h-9 w-9 px-0\"\n    >\n      <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n      <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n      <span className=\"sr-only\">Toggle theme</span>\n    </Button>\n  )\n}"}, {"name": "theme-toggle-dropdown.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport { Monitor, Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggleDropdown() {\n  const { setTheme, theme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <Button variant=\"ghost\" size=\"sm\" className=\"h-9 w-9 px-0\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">Toggle theme</span>\n      </Button>\n    )\n  }\n\n  const getCurrentIcon = () => {\n    if (theme === \"light\") return <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n    if (theme === \"dark\") return <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n    return <Monitor className=\"h-[1.2rem] w-[1.2rem]\" />\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"h-9 w-9 px-0\">\n          {getCurrentIcon()}\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}"}, {"name": "theme-aware.tsx", "content": "\"use client\"\n\nimport * as React from \"react\"\nimport { useTheme } from \"next-themes\"\n\ninterface ThemeAwareProps {\n  children: React.ReactNode\n  light?: React.ReactNode\n  dark?: React.ReactNode\n  system?: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ThemeAware({ \n  children, \n  light, \n  dark, \n  system, \n  fallback = null \n}: ThemeAwareProps) {\n  const { theme, resolvedTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return fallback ? <>{fallback}</> : null\n  }\n\n  if (theme === \"light\" && light) return <>{light}</>\n  if (theme === \"dark\" && dark) return <>{dark}</>\n  if (theme === \"system\" && system) return <>{system}</>\n\n  if (typeof children === \"function\") {\n    return <>{children({ theme, resolvedTheme })}</>\n  }\n\n  return <>{children}</>\n}\n\nexport function useThemeAware() {\n  const { theme, resolvedTheme, setTheme, systemTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  const isDark = mounted && resolvedTheme === \"dark\"\n  const isLight = mounted && resolvedTheme === \"light\"\n  const isSystem = mounted && theme === \"system\"\n\n  return {\n    theme,\n    resolvedTheme: mounted ? resolvedTheme : undefined,\n    systemTheme: mounted ? systemTheme : undefined,\n    setTheme,\n    isDark,\n    isLight,\n    isSystem,\n    mounted,\n  }\n}"}], "tailwind": {"config": {"darkMode": ["class"]}}, "cssVars": {"light": {"--background": "0 0% 100%", "--foreground": "222.2 84% 4.9%", "--card": "0 0% 100%", "--card-foreground": "222.2 84% 4.9%", "--popover": "0 0% 100%", "--popover-foreground": "222.2 84% 4.9%", "--primary": "222.2 47.4% 11.2%", "--primary-foreground": "210 40% 98%", "--secondary": "210 40% 96%", "--secondary-foreground": "222.2 47.4% 11.2%", "--muted": "210 40% 96%", "--muted-foreground": "215.4 16.3% 46.9%", "--accent": "210 40% 96%", "--accent-foreground": "222.2 47.4% 11.2%", "--destructive": "0 84.2% 60.2%", "--destructive-foreground": "210 40% 98%", "--border": "214.3 31.8% 91.4%", "--input": "214.3 31.8% 91.4%", "--ring": "222.2 84% 4.9%"}, "dark": {"--background": "222.2 84% 4.9%", "--foreground": "210 40% 98%", "--card": "222.2 84% 4.9%", "--card-foreground": "210 40% 98%", "--popover": "222.2 84% 4.9%", "--popover-foreground": "210 40% 98%", "--primary": "210 40% 98%", "--primary-foreground": "222.2 47.4% 11.2%", "--secondary": "217.2 32.6% 17.5%", "--secondary-foreground": "210 40% 98%", "--muted": "217.2 32.6% 17.5%", "--muted-foreground": "215 20.2% 65.1%", "--accent": "217.2 32.6% 17.5%", "--accent-foreground": "210 40% 98%", "--destructive": "0 62.8% 30.6%", "--destructive-foreground": "210 40% 98%", "--border": "217.2 32.6% 17.5%", "--input": "217.2 32.6% 17.5%", "--ring": "212.7 26.8% 83.9%"}}}