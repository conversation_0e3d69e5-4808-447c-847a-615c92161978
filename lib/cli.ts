/**
 * Supreme Toolkit CLI Utilities
 * 
 * This file contains utilities for installing and managing Supreme Toolkit modules.
 * These utilities work with the shadcn CLI system.
 */

import fs from 'fs/promises';
import path from 'path';
import { RegistryModule, loadRegistryModule, getFileTargetPath, validateRegistryModule } from './registry';

// ============================================================================
// CLI TYPES
// ============================================================================

export interface InstallOptions {
  force?: boolean;
  dryRun?: boolean;
  verbose?: boolean;
  skipDependencies?: boolean;
}

export interface InstallResult {
  success: boolean;
  module?: RegistryModule;
  filesCreated: string[];
  filesSkipped: string[];
  errors: string[];
  warnings: string[];
}

// ============================================================================
// FILE SYSTEM UTILITIES
// ============================================================================

/**
 * Check if a file exists
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Create directory if it doesn't exist
 */
export async function ensureDirectory(dirPath: string): Promise<void> {
  try {
    await fs.mkdir(dirPath, { recursive: true });
  } catch (error) {
    throw new Error(`Failed to create directory ${dirPath}: ${error}`);
  }
}

/**
 * Write file with directory creation
 */
export async function writeFileWithDir(filePath: string, content: string): Promise<void> {
  const dir = path.dirname(filePath);
  await ensureDirectory(dir);
  await fs.writeFile(filePath, content, 'utf-8');
}

/**
 * Read file content
 */
export async function readFile(filePath: string): Promise<string> {
  try {
    return await fs.readFile(filePath, 'utf-8');
  } catch (error) {
    throw new Error(`Failed to read file ${filePath}: ${error}`);
  }
}

// ============================================================================
// DEPENDENCY MANAGEMENT
// ============================================================================

/**
 * Install npm dependencies
 */
export async function installDependencies(
  dependencies: string[],
  isDev: boolean = false
): Promise<{ success: boolean; error?: string }> {
  if (dependencies.length === 0) {
    return { success: true };
  }

  try {
    const { spawn } = await import('child_process');
    const command = 'npm';
    const args = ['install', ...(isDev ? ['--save-dev'] : []), ...dependencies];

    return new Promise((resolve) => {
      const child = spawn(command, args, { stdio: 'inherit' });
      
      child.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true });
        } else {
          resolve({ 
            success: false, 
            error: `npm install failed with code ${code}` 
          });
        }
      });

      child.on('error', (error) => {
        resolve({ 
          success: false, 
          error: `Failed to run npm install: ${error.message}` 
        });
      });
    });
  } catch (error) {
    return { 
      success: false, 
      error: `Failed to install dependencies: ${error}` 
    };
  }
}

// ============================================================================
// CONFIG MANAGEMENT
// ============================================================================

/**
 * Update config.tsx with new module configuration
 */
export async function updateConfig(moduleName: string, _moduleConfig: unknown): Promise<void> {
  const configPath = path.join(process.cwd(), 'config.tsx');
  
  try {
    const configContent = await readFile(configPath);
    
    // This is a simplified approach - in a real implementation,
    // you'd want to use an AST parser to properly modify the config
    const configComment = `  // ${moduleName}: {\n  //   // Add your ${moduleName} configuration here\n  // },\n`;
    
    // Find the toolkitConfig object and add the comment
    const updatedContent = configContent.replace(
      /export const toolkitConfig: ToolkitConfig = {/,
      `export const toolkitConfig: ToolkitConfig = {\n${configComment}`
    );
    
    await writeFileWithDir(configPath, updatedContent);
  } catch (error) {
    console.warn(`Could not update config.tsx: ${error}`);
  }
}

/**
 * Add environment variables to .env.example
 */
export async function updateEnvExample(envVars: string[]): Promise<void> {
  if (envVars.length === 0) return;

  const envPath = path.join(process.cwd(), '.env.example');
  
  try {
    let envContent = '';
    
    if (await fileExists(envPath)) {
      envContent = await readFile(envPath);
    }
    
    // Add new environment variables
    const newVars = envVars
      .filter(varName => !envContent.includes(varName))
      .map(varName => `${varName}=your_${varName.toLowerCase()}_here`)
      .join('\n');
    
    if (newVars) {
      envContent += `\n# Added by Supreme Toolkit\n${newVars}\n`;
      await writeFileWithDir(envPath, envContent);
    }
  } catch (error) {
    console.warn(`Could not update .env.example: ${error}`);
  }
}

// ============================================================================
// MODULE INSTALLATION
// ============================================================================

/**
 * Install a Supreme Toolkit module
 */
export async function installModule(
  moduleName: string,
  options: InstallOptions = {}
): Promise<InstallResult> {
  const result: InstallResult = {
    success: false,
    filesCreated: [],
    filesSkipped: [],
    errors: [],
    warnings: [],
  };

  try {
    // Load module from registry
    const module = await loadRegistryModule(moduleName);
    if (!module) {
      result.errors.push(`Module "${moduleName}" not found in registry`);
      return result;
    }

    result.module = module;

    if (options.verbose) {
      console.log(`Installing module: ${module.name}`);
      console.log(`Description: ${module.meta.description}`);
      console.log(`Version: ${module.meta.version}`);
    }

    // Validate module
    if (!validateRegistryModule(module)) {
      result.errors.push(`Invalid module format: ${moduleName}`);
      return result;
    }

    // Install dependencies
    if (!options.skipDependencies) {
      if (module.dependencies.length > 0) {
        if (options.verbose) {
          console.log(`Installing dependencies: ${module.dependencies.join(', ')}`);
        }
        
        if (!options.dryRun) {
          const depResult = await installDependencies(module.dependencies, false);
          if (!depResult.success) {
            result.errors.push(depResult.error || 'Failed to install dependencies');
            return result;
          }
        }
      }

      if (module.devDependencies.length > 0) {
        if (options.verbose) {
          console.log(`Installing dev dependencies: ${module.devDependencies.join(', ')}`);
        }
        
        if (!options.dryRun) {
          const devDepResult = await installDependencies(module.devDependencies, true);
          if (!devDepResult.success) {
            result.warnings.push(devDepResult.error || 'Failed to install dev dependencies');
          }
        }
      }
    }

    // Install files
    for (const file of module.files) {
      const targetPath = getFileTargetPath(file);
      const fullPath = path.join(process.cwd(), targetPath);

      if (options.verbose) {
        console.log(`Processing file: ${targetPath}`);
      }

      // Check if file already exists
      if (await fileExists(fullPath) && !options.force) {
        result.filesSkipped.push(targetPath);
        result.warnings.push(`File already exists: ${targetPath} (use --force to overwrite)`);
        continue;
      }

      // Create file
      if (!options.dryRun) {
        try {
          await writeFileWithDir(fullPath, file.content);
          result.filesCreated.push(targetPath);
        } catch (error) {
          result.errors.push(`Failed to create file ${targetPath}: ${error}`);
        }
      } else {
        result.filesCreated.push(targetPath);
      }
    }

    // Update configuration files
    if (!options.dryRun) {
      // Update config.tsx
      await updateConfig(moduleName, {});

      // Update .env.example
      if (module.envVars) {
        await updateEnvExample([...module.envVars.required, ...module.envVars.optional]);
      }
    }

    result.success = result.errors.length === 0;

    if (options.verbose) {
      console.log(`Installation ${result.success ? 'completed' : 'failed'}`);
      console.log(`Files created: ${result.filesCreated.length}`);
      console.log(`Files skipped: ${result.filesSkipped.length}`);
      console.log(`Errors: ${result.errors.length}`);
      console.log(`Warnings: ${result.warnings.length}`);
    }

    return result;

  } catch (error) {
    result.errors.push(`Installation failed: ${error}`);
    return result;
  }
}

/**
 * List available modules
 */
export async function listModules(): Promise<string[]> {
  try {
    const registryPath = path.join(process.cwd(), 'registry', 'supremetoolkit');
    const files = await fs.readdir(registryPath);
    return files
      .filter(file => file.endsWith('.json'))
      .map(file => file.replace('.json', ''));
  } catch (error) {
    console.error('Failed to list modules:', error);
    return [];
  }
}

/**
 * Get module information
 */
export async function getModuleInfo(moduleName: string): Promise<RegistryModule | null> {
  return await loadRegistryModule(moduleName);
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validate project structure for Supreme Toolkit
 */
export async function validateProject(): Promise<{ valid: boolean; issues: string[] }> {
  const issues: string[] = [];

  // Check for required files
  const requiredFiles = [
    'package.json',
    'next.config.ts',
    'tsconfig.json',
    'config.tsx',
  ];

  for (const file of requiredFiles) {
    if (!(await fileExists(path.join(process.cwd(), file)))) {
      issues.push(`Missing required file: ${file}`);
    }
  }

  // Check for required directories
  const requiredDirs = [
    'app',
    'components',
    'lib',
    'types',
  ];

  for (const dir of requiredDirs) {
    try {
      const stats = await fs.stat(path.join(process.cwd(), dir));
      if (!stats.isDirectory()) {
        issues.push(`${dir} should be a directory`);
      }
    } catch {
      issues.push(`Missing required directory: ${dir}`);
    }
  }

  return {
    valid: issues.length === 0,
    issues,
  };
}
