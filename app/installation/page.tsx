import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function InstallationPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Navigation */}
      <nav className="border-b bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-gray-900 dark:text-white">
                🚀 Supreme Toolkit
              </span>
              <Badge variant="secondary" className="text-xs">
                Beta
              </Badge>
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/docs" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                Documentation
              </Link>
              <Link href="/" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                Home
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-16 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Installation Guide
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Get started with Supreme Toolkit in minutes
          </p>
        </div>

        {/* Prerequisites */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📋 Prerequisites
            </CardTitle>
            <CardDescription>
              Make sure you have these set up before installing Supreme Toolkit modules
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-green-600 dark:text-green-400 text-sm">✓</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Next.js 13+ with App Router</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Supreme Toolkit requires Next.js 13 or later with the App Router</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-green-600 dark:text-green-400 text-sm">✓</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">shadcn/ui installed</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Supreme Toolkit extends shadcn/ui components</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-green-600 dark:text-green-400 text-sm">✓</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">TypeScript (recommended)</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">All modules include full TypeScript support</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Setup */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              ⚡ Quick Setup
            </CardTitle>
            <CardDescription>
              If you don't have shadcn/ui set up yet, here's how to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">1. Initialize shadcn/ui</h4>
                <div className="bg-gray-900 dark:bg-gray-800 p-4 rounded-lg">
                  <code className="text-green-400 text-sm">
                    npx shadcn-ui@latest init
                  </code>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">2. Install base components</h4>
                <div className="bg-gray-900 dark:bg-gray-800 p-4 rounded-lg">
                  <code className="text-green-400 text-sm">
                    npx shadcn-ui@latest add button card input label
                  </code>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">3. You're ready to install Supreme Toolkit modules!</h4>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Module Installation */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📦 Installing Modules
            </CardTitle>
            <CardDescription>
              Each module can be installed independently using the shadcn CLI
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Auth Module */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                    🔐 Authentication Module
                  </h4>
                  <Badge variant="default">Ready</Badge>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Complete auth system with betterAuth, multiple providers, and guards
                </p>
                <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded">
                  <code className="text-green-400 text-sm">
                    npx shadcn@latest add "https://supremetoolkit.in/r/auth-module"
                  </code>
                </div>
              </div>

              {/* Mailer Module */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                    📧 Mailer Module
                  </h4>
                  <Badge variant="default">Ready</Badge>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Universal email system with Resend and Nodemailer support
                </p>
                <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded">
                  <code className="text-green-400 text-sm">
                    npx shadcn@latest add "https://supremetoolkit.in/r/mailer-module"
                  </code>
                </div>
              </div>

              {/* Waitlist Module */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                    📝 Waitlist Module
                  </h4>
                  <Badge variant="default">Ready</Badge>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Complete waitlist system with validation and email confirmations
                </p>
                <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded">
                  <code className="text-green-400 text-sm">
                    npx shadcn@latest add "https://supremetoolkit.in/r/waitlist-module"
                  </code>
                </div>
              </div>

              {/* Stripe Modules */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                    💳 Stripe Modules
                  </h4>
                  <Badge variant="default">Ready</Badge>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Choose the payment features you need
                </p>
                <div className="space-y-2">
                  <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded">
                    <div className="text-gray-300 text-xs mb-1"># One-time payments</div>
                    <code className="text-green-400 text-sm">
                      npx shadcn@latest add "https://supremetoolkit.in/r/one-time-payment"
                    </code>
                  </div>
                  <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded">
                    <div className="text-gray-300 text-xs mb-1"># Subscription management</div>
                    <code className="text-green-400 text-sm">
                      npx shadcn@latest add "https://supremetoolkit.in/r/subscriptions"
                    </code>
                  </div>
                  <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded">
                    <div className="text-gray-300 text-xs mb-1"># Customer portal</div>
                    <code className="text-green-400 text-sm">
                      npx shadcn@latest add "https://supremetoolkit.in/r/customer-portal"
                    </code>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* What Gets Installed */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📁 What Gets Installed
            </CardTitle>
            <CardDescription>
              Each module installs multiple files across your project
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Frontend Files</h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    UI Components in <code>components/ui/</code>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                    React Hooks in <code>hooks/</code>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                    Utility Libraries in <code>lib/</code>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                    TypeScript Types in <code>types/</code>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Backend Files</h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                    API Routes in <code>app/api/</code>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                    Server Actions in <code>actions/</code>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full"></span>
                    Configuration in <code>config.tsx</code>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🚀 Next Steps
            </CardTitle>
            <CardDescription>
              After installing your first module
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 dark:text-blue-400 text-sm">1</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Configure Environment Variables</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Set up API keys and configuration in your <code>.env.local</code> file</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 dark:text-blue-400 text-sm">2</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Update Configuration</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Modify <code>config.tsx</code> to customize module behavior</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 dark:text-blue-400 text-sm">3</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Start Using Components</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">Import and use the components in your pages and layouts</p>
                </div>
              </div>
            </div>
            
            <div className="flex gap-4 mt-6">
              <Button asChild>
                <Link href="/docs">
                  📚 Read Documentation
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/">
                  🏠 Back to Home
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
