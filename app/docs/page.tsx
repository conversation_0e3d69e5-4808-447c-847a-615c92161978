import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function DocsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Navigation */}
      <nav className="border-b bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-gray-900 dark:text-white">
                🚀 Supreme Toolkit
              </span>
              <Badge variant="secondary" className="text-xs">
                Beta
              </Badge>
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/installation" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                Installation
              </Link>
              <Link href="/" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                Home
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-16 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Documentation
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Complete guide to using Supreme Toolkit modules
          </p>
        </div>

        {/* Quick Start */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              ⚡ Quick Start
            </CardTitle>
            <CardDescription>
              Get up and running in 5 minutes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">1. Install a Module</h4>
                <div className="bg-gray-900 dark:bg-gray-800 p-4 rounded-lg mb-3">
                  <code className="text-green-400 text-sm">
                    npx shadcn@latest add "https://supremetoolkit.in/r/waitlist-module"
                  </code>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  This installs the waitlist component, hook, API route, and server actions
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">2. Use the Component</h4>
                <div className="bg-gray-900 dark:bg-gray-800 p-4 rounded-lg mb-3">
                  <code className="text-blue-400 text-sm whitespace-pre">
{`import { WaitlistForm } from "@/components/ui/waitlist-form";

export default function Page() {
  return <WaitlistForm />;
}`}
                  </code>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Import and use the component in your pages
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Module Documentation */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {/* Auth Module */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🔐 Authentication Module
                <Badge variant="default">Ready</Badge>
              </CardTitle>
              <CardDescription>
                Complete authentication system with betterAuth
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Features</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <li>• Multiple OAuth providers (Google, GitHub)</li>
                    <li>• Email/password authentication</li>
                    <li>• Auth guards and protected routes</li>
                    <li>• User profile management</li>
                    <li>• Session management</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Components</h4>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">SignIn</Badge>
                    <Badge variant="secondary" className="text-xs">SignUp</Badge>
                    <Badge variant="secondary" className="text-xs">SignOut</Badge>
                    <Badge variant="secondary" className="text-xs">AuthGuards</Badge>
                    <Badge variant="secondary" className="text-xs">UserProfile</Badge>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Installation</h4>
                  <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded text-xs">
                    <code className="text-green-400">
                      npx shadcn@latest add "https://supremetoolkit.in/r/auth-module"
                    </code>
                  </div>
                </div>
                <Button asChild size="sm" className="w-full">
                  <Link href="/auth-demo">View Demo</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Mailer Module */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📧 Mailer Module
                <Badge variant="default">Ready</Badge>
              </CardTitle>
              <CardDescription>
                Universal email system with auto-detection
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Features</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <li>• Auto-detects Resend or Nodemailer</li>
                    <li>• Email templates with React Email</li>
                    <li>• SMTP and Gmail support</li>
                    <li>• Testing components</li>
                    <li>• Error handling and fallbacks</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Components</h4>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">EmailTemplates</Badge>
                    <Badge variant="secondary" className="text-xs">MailerTest</Badge>
                    <Badge variant="secondary" className="text-xs">useMailer</Badge>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Installation</h4>
                  <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded text-xs">
                    <code className="text-green-400">
                      npx shadcn@latest add "https://supremetoolkit.in/r/mailer-module"
                    </code>
                  </div>
                </div>
                <Button asChild size="sm" variant="outline" className="w-full">
                  <Link href="/docs/mailer">View Docs</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Waitlist Module */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📝 Waitlist Module
                <Badge variant="default">Ready</Badge>
              </CardTitle>
              <CardDescription>
                Complete waitlist system with validation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Features</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <li>• Email validation and duplicate checking</li>
                    <li>• Customizable form components</li>
                    <li>• Server actions for processing</li>
                    <li>• Email confirmations</li>
                    <li>• Success/error states</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Components</h4>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">WaitlistForm</Badge>
                    <Badge variant="secondary" className="text-xs">useWaitlist</Badge>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Installation</h4>
                  <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded text-xs">
                    <code className="text-green-400">
                      npx shadcn@latest add "https://supremetoolkit.in/r/waitlist-module"
                    </code>
                  </div>
                </div>
                <Button asChild size="sm" className="w-full">
                  <Link href="/waitlist-demo">View Demo</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Config Module */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                ⚙️ Config Module
                <Badge variant="default">Ready</Badge>
              </CardTitle>
              <CardDescription>
                Central configuration system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Features</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <li>• Centralized API key management</li>
                    <li>• Module-specific configurations</li>
                    <li>• TypeScript definitions</li>
                    <li>• Environment variable helpers</li>
                    <li>• Validation and defaults</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Files</h4>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">config.tsx</Badge>
                    <Badge variant="secondary" className="text-xs">types/index.ts</Badge>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Installation</h4>
                  <div className="bg-gray-900 dark:bg-gray-800 p-3 rounded text-xs">
                    <code className="text-green-400">
                      npx shadcn@latest add "https://supremetoolkit.in/r/config-module"
                    </code>
                  </div>
                </div>
                <Button asChild size="sm" variant="outline" className="w-full">
                  <Link href="/docs/config">View Docs</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stripe Modules Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              💳 Stripe Payment Modules
            </CardTitle>
            <CardDescription>
              Modular payment solutions - install only what you need
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              {/* One-Time Payment */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">One-Time Payment</h4>
                  <Badge variant="default">Ready</Badge>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Simple payments for products and services
                </p>
                <div className="space-y-2 mb-4">
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">PayButton</Badge>
                    <Badge variant="secondary" className="text-xs">Checkout</Badge>
                    <Badge variant="secondary" className="text-xs">Webhooks</Badge>
                  </div>
                </div>
                <div className="bg-gray-900 dark:bg-gray-800 p-2 rounded text-xs mb-3">
                  <code className="text-green-400">
                    npx shadcn@latest add "https://supremetoolkit.in/r/one-time-payment"
                  </code>
                </div>
                <Button asChild size="sm" variant="outline" className="w-full">
                  <Link href="/docs/payments">Docs</Link>
                </Button>
              </div>

              {/* Subscriptions */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">Subscriptions</h4>
                  <Badge variant="default">Ready</Badge>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Complete subscription management system
                </p>
                <div className="space-y-2 mb-4">
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">PricingCard</Badge>
                    <Badge variant="secondary" className="text-xs">SubManager</Badge>
                    <Badge variant="secondary" className="text-xs">Cancel/Reactivate</Badge>
                  </div>
                </div>
                <div className="bg-gray-900 dark:bg-gray-800 p-2 rounded text-xs mb-3">
                  <code className="text-green-400">
                    npx shadcn@latest add "https://supremetoolkit.in/r/subscriptions"
                  </code>
                </div>
                <Button asChild size="sm" variant="outline" className="w-full">
                  <Link href="/docs/subscriptions">Docs</Link>
                </Button>
              </div>

              {/* Customer Portal */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">Customer Portal</h4>
                  <Badge variant="default">Ready</Badge>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Self-service billing management
                </p>
                <div className="space-y-2 mb-4">
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">Portal Access</Badge>
                    <Badge variant="secondary" className="text-xs">Billing</Badge>
                    <Badge variant="secondary" className="text-xs">Invoices</Badge>
                  </div>
                </div>
                <div className="bg-gray-900 dark:bg-gray-800 p-2 rounded text-xs mb-3">
                  <code className="text-green-400">
                    npx shadcn@latest add "https://supremetoolkit.in/r/customer-portal"
                  </code>
                </div>
                <Button asChild size="sm" variant="outline" className="w-full">
                  <Link href="/docs/customer-portal">Docs</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Configuration Guide */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔧 Configuration
            </CardTitle>
            <CardDescription>
              How to configure modules after installation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Environment Variables</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Add these to your <code>.env.local</code> file:
                </p>
                <div className="bg-gray-900 dark:bg-gray-800 p-4 rounded-lg">
                  <code className="text-green-400 text-sm whitespace-pre">
{`# Authentication (betterAuth)
BETTER_AUTH_SECRET=your-secret-key
BETTER_AUTH_URL=http://localhost:3000
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Email (Resend or SMTP)
RESEND_API_KEY=your-resend-api-key
# OR for SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...`}
                  </code>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Module Configuration</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Customize module behavior in <code>config.tsx</code>:
                </p>
                <div className="bg-gray-900 dark:bg-gray-800 p-4 rounded-lg">
                  <code className="text-blue-400 text-sm whitespace-pre">
{`export const moduleConfig = {
  auth: {
    providers: ['google', 'github', 'email'],
    sessionDuration: 60 * 60 * 24 * 30, // 30 days
  },
  mailer: {
    defaultFrom: '<EMAIL>',
    provider: 'resend', // or 'nodemailer'
  },
  stripe: {
    currency: 'usd',
    successUrl: '/success',
    cancelUrl: '/cancel',
  },
};`}
                  </code>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Support */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              💬 Support & Community
            </CardTitle>
            <CardDescription>
              Get help and connect with other developers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Resources</h4>
                <ul className="space-y-2 text-sm">
                  <li>
                    <Link href="/installation" className="text-blue-600 dark:text-blue-400 hover:underline">
                      📦 Installation Guide
                    </Link>
                  </li>
                  <li>
                    <Link href="/auth-demo" className="text-blue-600 dark:text-blue-400 hover:underline">
                      🔐 Auth Demo
                    </Link>
                  </li>
                  <li>
                    <Link href="/waitlist-demo" className="text-blue-600 dark:text-blue-400 hover:underline">
                      📝 Waitlist Demo
                    </Link>
                  </li>
                  <li>
                    <Link href="/" className="text-blue-600 dark:text-blue-400 hover:underline">
                      🏠 Homepage
                    </Link>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Coming Soon</h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                  <li>• GitHub Discussions</li>
                  <li>• Discord Community</li>
                  <li>• Video Tutorials</li>
                  <li>• Advanced Examples</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
