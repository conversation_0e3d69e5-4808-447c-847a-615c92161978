{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "auth-module", "type": "registry:component", "title": "Authentication Module", "description": "Complete authentication system with betterAuth, multiple providers (Google, GitHub, email/password), and auth guards", "dependencies": ["better-auth", "better-sqlite3"], "registryDependencies": ["button", "input", "label", "card", "avatar", "separator"], "files": [{"path": "lib/auth.ts", "content": "/**\n * Supreme Toolkit - Better Auth Configuration\n * \n * This file configures betterAuth for the Supreme Toolkit auth module.\n * It provides a complete authentication system with multiple providers.\n */\n\nimport { betterAuth } from \"better-auth\";\nimport { getModuleConfig } from \"@/config\";\nimport Database from \"better-sqlite3\";\nimport path from \"path\";\n\n// Get auth configuration from Supreme Toolkit config\nfunction getAuthConfig() {\n  try {\n    return getModuleConfig('auth');\n  } catch {\n    return {\n      providers: ['email'] as ('google' | 'github' | 'email')[],\n      sessionDuration: 60 * 60 * 24 * 30 // 30 days\n    };\n  }\n}\n\nconst authConfig = getAuthConfig();\n\n// Create SQLite database for demo purposes\n// In production, you'd use PostgreSQL, MySQL, or another database\nconst dbPath = path.join(process.cwd(), 'auth.db');\nconst database = new Database(dbPath);\n\nexport const auth = betterAuth({\n  // Database configuration\n  database,\n  \n  // Base URL for the application\n  baseURL: process.env.BETTER_AUTH_URL || process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3000\",\n  \n  // Secret for encryption and signing\n  secret: process.env.BETTER_AUTH_SECRET || \"your-secret-key-change-in-production\",\n  \n  // Email and password authentication\n  emailAndPassword: {\n    enabled: true,\n    requireEmailVerification: false, // Set to true in production\n  },\n  \n  // Social providers configuration\n  socialProviders: {\n    // Google OAuth\n    ...(authConfig?.providers?.includes('google') && {\n      google: {\n        clientId: process.env.GOOGLE_CLIENT_ID as string,\n        clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,\n      }\n    }),\n    \n    // GitHub OAuth\n    ...(authConfig?.providers?.includes('github') && {\n      github: {\n        clientId: process.env.GITHUB_CLIENT_ID as string,\n        clientSecret: process.env.GITHUB_CLIENT_SECRET as string,\n      }\n    }),\n  },\n  \n  // Session configuration\n  session: {\n    expiresIn: authConfig?.sessionDuration || 60 * 60 * 24 * 30, // 30 days\n    updateAge: 60 * 60 * 24, // Update session every 24 hours\n  },\n  \n  // User configuration\n  user: {\n    additionalFields: {\n      role: {\n        type: \"string\",\n        defaultValue: \"user\",\n      },\n      avatar: {\n        type: \"string\",\n        required: false,\n      },\n    },\n  },\n  \n  // Advanced configuration\n  advanced: {\n    generateId: () => {\n      // Generate a custom ID (you can use nanoid, uuid, etc.)\n      return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    },\n  },\n  \n  // Callbacks for custom logic\n  callbacks: {\n    async signUp({ user, account }: { user: any; account: any }) {\n      // Custom logic after user signs up\n      console.log(`New user signed up: ${user.email}`);\n      return { user, account };\n    },\n\n    async signIn({ user, account, session }: { user: any; account: any; session: any }) {\n      // Custom logic after user signs in\n      console.log(`User signed in: ${user.email}`);\n      return { user, account, session };\n    },\n  },\n});\n\n// Export types for TypeScript\nexport type Session = typeof auth.$Infer.Session;\nexport type User = typeof auth.$Infer.Session.user;\n", "type": "registry:lib"}, {"path": "lib/auth-client.ts", "content": "/**\n * Supreme Toolkit - Better Auth Client\n * \n * This file configures the betterAuth client for React components.\n * It provides hooks and methods for client-side authentication.\n */\n\nimport { createAuthClient } from \"better-auth/react\";\n\n// Create the auth client\nexport const authClient = createAuthClient({\n  baseURL: process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3000\",\n});\n\n// Export commonly used methods and hooks\nexport const {\n  signIn,\n  signUp,\n  signOut,\n  useSession,\n  getSession,\n} = authClient;\n", "type": "registry:lib"}, {"path": "hooks/use-auth.ts", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { authClient } from \"@/lib/auth-client\";\nimport { onUserSignup, onUserLogin, onUserLogout } from \"@/actions/auth-actions\";\n\ninterface UseAuthOptions {\n  onSuccess?: (data: any) => void;\n  onError?: (error: string) => void;\n}\n\ninterface SignInParams {\n  email: string;\n  password: string;\n}\n\ninterface SignUpParams {\n  name: string;\n  email: string;\n  password: string;\n}\n\nexport function useAuth(options: UseAuthOptions = {}) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Get session from authClient\n  const { data: session, isPending: sessionLoading } = authClient.useSession();\n\n  const signIn = async (params: SignInParams) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await authClient.signIn.email({\n        email: params.email,\n        password: params.password,\n      });\n\n      if (result.error) {\n        throw new Error(result.error.message || 'Sign in failed');\n      }\n\n      // Call server action for custom logic\n      if (result.data?.user) {\n        await onUserLogin({\n          user: result.data.user,\n          provider: 'email',\n          isFirstLogin: false, // You might want to track this\n          timestamp: new Date(),\n        });\n      }\n\n      options.onSuccess?.(result.data);\n      return { success: true, data: result.data };\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signUp = async (params: SignUpParams) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await authClient.signUp.email({\n        email: params.email,\n        password: params.password,\n        name: params.name,\n      });\n\n      if (result.error) {\n        throw new Error(result.error.message || 'Sign up failed');\n      }\n\n      // Call server action for custom logic\n      if (result.data?.user) {\n        await onUserSignup({\n          user: result.data.user,\n          provider: 'email',\n          metadata: { name: params.name },\n          timestamp: new Date(),\n        });\n      }\n\n      options.onSuccess?.(result.data);\n      return { success: true, data: result.data };\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signInWithProvider = async (provider: string) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await authClient.signIn.social({\n        provider: provider as any,\n        callbackURL: window.location.origin + '/dashboard',\n      });\n\n      if (result.error) {\n        throw new Error(result.error.message || 'Social sign in failed');\n      }\n\n      // Note: For social sign-in, the callback will handle the server action\n      options.onSuccess?.(result.data);\n      return { success: true, data: result.data };\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signOut = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Call server action before signing out\n      if (session?.user) {\n        await onUserLogout({\n          userId: session.user.id,\n          sessionId: session.session.id,\n          timestamp: new Date(),\n        });\n      }\n\n      const result = await authClient.signOut();\n\n      if (result.error) {\n        throw new Error(result.error.message || 'Sign out failed');\n      }\n\n      options.onSuccess?.(result.data);\n      return { success: true, data: result.data };\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const reset = () => {\n    setError(null);\n  };\n\n  return {\n    // State\n    session,\n    user: session?.user || null,\n    isAuthenticated: !!session?.user,\n    isLoading: isLoading || sessionLoading,\n    error,\n\n    // Actions\n    signIn,\n    signUp,\n    signInWithProvider,\n    signOut,\n    reset,\n  };\n}\n", "type": "registry:hook"}, {"path": "components/ui/auth-signin.tsx", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { Loader2, Mail, Github } from \"lucide-react\";\nimport { getModuleConfig } from \"@/config\";\n\ninterface AuthSignInProps {\n  title?: string;\n  description?: string;\n  onSuccess?: () => void;\n  onSignUpClick?: () => void;\n  className?: string;\n}\n\nexport function AuthSignIn({\n  title = \"Sign In\",\n  description = \"Enter your credentials to access your account\",\n  onSuccess,\n  onSignUpClick,\n  className,\n}: AuthSignInProps) {\n  const [email, setEmail] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const { signIn, signInWithProvider, isLoading, error } = useAuth();\n  \n  const authConfig = getModuleConfig('auth');\n  const providers = authConfig?.providers || [];\n\n  const handleEmailSignIn = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email || !password) return;\n    \n    const result = await signIn({ email, password });\n    if (result.success) {\n      onSuccess?.();\n    }\n  };\n\n  const handleProviderSignIn = async (provider: string) => {\n    const result = await signInWithProvider(provider);\n    if (result.success) {\n      onSuccess?.();\n    }\n  };\n\n  return (\n    <Card className={className}>\n      <CardHeader className=\"space-y-1\">\n        <CardTitle className=\"text-2xl text-center\">{title}</CardTitle>\n        <CardDescription className=\"text-center\">\n          {description}\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Social Sign In */}\n        {providers.length > 0 && (\n          <div className=\"space-y-2\">\n            {providers.includes('google') && (\n              <Button\n                variant=\"outline\"\n                className=\"w-full\"\n                onClick={() => handleProviderSignIn('google')}\n                disabled={isLoading}\n              >\n                <Mail className=\"mr-2 h-4 w-4\" />\n                Continue with Google\n              </Button>\n            )}\n            \n            {providers.includes('github') && (\n              <Button\n                variant=\"outline\"\n                className=\"w-full\"\n                onClick={() => handleProviderSignIn('github')}\n                disabled={isLoading}\n              >\n                <Github className=\"mr-2 h-4 w-4\" />\n                Continue with GitHub\n              </Button>\n            )}\n          </div>\n        )}\n\n        {/* Separator */}\n        {providers.length > 0 && providers.includes('email') && (\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <Separator className=\"w-full\" />\n            </div>\n            <div className=\"relative flex justify-center text-xs uppercase\">\n              <span className=\"bg-background px-2 text-muted-foreground\">\n                Or continue with email\n              </span>\n            </div>\n          </div>\n        )}\n\n        {/* Email Sign In */}\n        {providers.includes('email') && (\n          <form onSubmit={handleEmailSignIn} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">Password</Label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                placeholder=\"Enter your password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            \n            {error && (\n              <div className=\"text-sm text-red-600 bg-red-50 dark:bg-red-900/20 p-3 rounded-md\">\n                {error}\n              </div>\n            )}\n            \n            <Button type=\"submit\" disabled={isLoading || !email || !password} className=\"w-full\">\n              {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n              Sign In\n            </Button>\n          </form>\n        )}\n\n        {/* Sign Up Link */}\n        {onSignUpClick && (\n          <div className=\"text-center text-sm\">\n            <span className=\"text-muted-foreground\">Don't have an account? </span>\n            <Button\n              variant=\"link\"\n              className=\"p-0 h-auto font-normal\"\n              onClick={onSignUpClick}\n              disabled={isLoading}\n            >\n              Sign up\n            </Button>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n", "type": "registry:component"}, {"path": "components/ui/auth-signup.tsx", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { Loader2, Mail, Github } from \"lucide-react\";\nimport { getModuleConfig } from \"@/config\";\n\ninterface AuthSignUpProps {\n  title?: string;\n  description?: string;\n  onSuccess?: () => void;\n  onSignInClick?: () => void;\n  className?: string;\n}\n\nexport function AuthSignUp({\n  title = \"Create Account\",\n  description = \"Enter your information to create a new account\",\n  onSuccess,\n  onSignInClick,\n  className,\n}: AuthSignUpProps) {\n  const [name, setName] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\n  const { signUp, signInWithProvider, isLoading, error } = useAuth();\n  \n  const authConfig = getModuleConfig('auth');\n  const providers = authConfig?.providers || [];\n\n  const handleEmailSignUp = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!name || !email || !password) return;\n    \n    if (password !== confirmPassword) {\n      return;\n    }\n    \n    const result = await signUp({ name, email, password });\n    if (result.success) {\n      onSuccess?.();\n    }\n  };\n\n  const handleProviderSignIn = async (provider: string) => {\n    const result = await signInWithProvider(provider);\n    if (result.success) {\n      onSuccess?.();\n    }\n  };\n\n  const passwordsMatch = password === confirmPassword;\n  const showPasswordError = confirmPassword && !passwordsMatch;\n\n  return (\n    <Card className={className}>\n      <CardHeader className=\"space-y-1\">\n        <CardTitle className=\"text-2xl text-center\">{title}</CardTitle>\n        <CardDescription className=\"text-center\">\n          {description}\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Social Sign Up */}\n        {providers.length > 0 && (\n          <div className=\"space-y-2\">\n            {providers.includes('google') && (\n              <Button\n                variant=\"outline\"\n                className=\"w-full\"\n                onClick={() => handleProviderSignIn('google')}\n                disabled={isLoading}\n              >\n                <Mail className=\"mr-2 h-4 w-4\" />\n                Continue with Google\n              </Button>\n            )}\n            \n            {providers.includes('github') && (\n              <Button\n                variant=\"outline\"\n                className=\"w-full\"\n                onClick={() => handleProviderSignIn('github')}\n                disabled={isLoading}\n              >\n                <Github className=\"mr-2 h-4 w-4\" />\n                Continue with GitHub\n              </Button>\n            )}\n          </div>\n        )}\n\n        {/* Separator */}\n        {providers.length > 0 && providers.includes('email') && (\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <Separator className=\"w-full\" />\n            </div>\n            <div className=\"relative flex justify-center text-xs uppercase\">\n              <span className=\"bg-background px-2 text-muted-foreground\">\n                Or continue with email\n              </span>\n            </div>\n          </div>\n        )}\n\n        {/* Email Sign Up */}\n        {providers.includes('email') && (\n          <form onSubmit={handleEmailSignUp} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"name\">Full Name</Label>\n              <Input\n                id=\"name\"\n                type=\"text\"\n                placeholder=\"Enter your full name\"\n                value={name}\n                onChange={(e) => setName(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">Password</Label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                placeholder=\"Create a password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"confirmPassword\">Confirm Password</Label>\n              <Input\n                id=\"confirmPassword\"\n                type=\"password\"\n                placeholder=\"Confirm your password\"\n                value={confirmPassword}\n                onChange={(e) => setConfirmPassword(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n              {showPasswordError && (\n                <p className=\"text-sm text-red-600\">Passwords do not match</p>\n              )}\n            </div>\n            \n            {error && (\n              <div className=\"text-sm text-red-600 bg-red-50 dark:bg-red-900/20 p-3 rounded-md\">\n                {error}\n              </div>\n            )}\n            \n            <Button \n              type=\"submit\" \n              disabled={isLoading || !name || !email || !password || !passwordsMatch} \n              className=\"w-full\"\n            >\n              {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n              Create Account\n            </Button>\n          </form>\n        )}\n\n        {/* Sign In Link */}\n        {onSignInClick && (\n          <div className=\"text-center text-sm\">\n            <span className=\"text-muted-foreground\">Already have an account? </span>\n            <Button\n              variant=\"link\"\n              className=\"p-0 h-auto font-normal\"\n              onClick={onSignInClick}\n              disabled={isLoading}\n            >\n              Sign in\n            </Button>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n", "type": "registry:component"}, {"path": "components/ui/auth-signout.tsx", "content": "\"use client\";\n\nimport { Button } from \"@/components/ui/button\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { LogOut, Loader2 } from \"lucide-react\";\n\ninterface AuthSignOutProps {\n  children?: React.ReactNode;\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\";\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\";\n  className?: string;\n  onSignOut?: () => void;\n  showIcon?: boolean;\n}\n\n/**\n * AuthSignOut Component\n * \n * A simple sign-out button that handles the sign-out process.\n * \n * @example\n * <AuthSignOut>Sign Out</AuthSignOut>\n * \n * @example\n * <AuthSignOut variant=\"outline\" size=\"sm\" onSignOut={() => router.push('/')}>\n *   Log Out\n * </AuthSignOut>\n */\nexport function AuthSignOut({\n  children = \"Sign Out\",\n  variant = \"outline\",\n  size = \"default\",\n  className,\n  onSignOut,\n  showIcon = true,\n}: AuthSignOutProps) {\n  const { signOut, isLoading } = useAuth();\n\n  const handleSignOut = async () => {\n    await signOut();\n    onSignOut?.();\n  };\n\n  return (\n    <Button\n      variant={variant}\n      size={size}\n      className={className}\n      onClick={handleSignOut}\n      disabled={isLoading}\n    >\n      {isLoading ? (\n        <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n      ) : (\n        showIcon && <LogOut className=\"mr-2 h-4 w-4\" />\n      )}\n      {children}\n    </Button>\n  );\n}\n", "type": "registry:component"}, {"path": "components/ui/auth-guards.tsx", "content": "\"use client\";\n\nimport { ReactNode } from \"react\";\nimport { useAuth } from \"@/hooks/use-auth\";\n\n\n\n// ============================================================================\n// AUTH GUARD COMPONENTS\n// ============================================================================\n\ninterface AuthGuardProps {\n  children: ReactNode;\n  fallback?: ReactNode;\n  loading?: ReactNode;\n}\n\n/**\n * SignedIn Component\n * \n * Renders children only when user is authenticated.\n * Optionally shows a fallback component when user is not authenticated.\n * \n * @example\n * <SignedIn fallback={<div>Please sign in</div>}>\n *   <UserDashboard />\n * </SignedIn>\n */\nexport function SignedIn({ children, fallback = null, loading = null }: AuthGuardProps) {\n  const { isAuthenticated, isLoading } = useAuth();\n\n  if (isLoading) {\n    return loading ? <>{loading}</> : null;\n  }\n\n  if (isAuthenticated) {\n    return <>{children}</>;\n  }\n\n  return fallback ? <>{fallback}</> : null;\n}\n\n/**\n * SignedOut Component\n * \n * Renders children only when user is NOT authenticated.\n * Optionally shows a fallback component when user is authenticated.\n * \n * @example\n * <SignedOut fallback={<div>You are already signed in</div>}>\n *   <AuthSignIn />\n * </SignedOut>\n */\nexport function SignedOut({ children, fallback = null, loading = null }: AuthGuardProps) {\n  const { isAuthenticated, isLoading } = useAuth();\n\n  if (isLoading) {\n    return loading ? <>{loading}</> : null;\n  }\n\n  if (!isAuthenticated) {\n    return <>{children}</>;\n  }\n\n  return fallback ? <>{fallback}</> : null;\n}\n\n/**\n * AuthLoading Component\n * \n * Renders children only when authentication state is loading.\n * Useful for showing loading spinners during auth checks.\n * \n * @example\n * <AuthLoading>\n *   <div>Checking authentication...</div>\n * </AuthLoading>\n */\nexport function AuthLoading({ children }: { children: ReactNode }) {\n  const { isLoading } = useAuth();\n\n  if (isLoading) {\n    return <>{children}</>;\n  }\n\n  return null;\n}\n\n/**\n * ProtectedRoute Component\n * \n * A more comprehensive route protection component that handles\n * authentication, loading states, and redirects.\n * \n * @example\n * <ProtectedRoute\n *   loading={<LoadingSpinner />}\n *   fallback={<AuthSignIn />}\n *   redirectTo=\"/login\"\n * >\n *   <Dashboard />\n * </ProtectedRoute>\n */\ninterface ProtectedRouteProps extends AuthGuardProps {\n  redirectTo?: string;\n  requireRole?: string | string[];\n}\n\nexport function ProtectedRoute({ \n  children, \n  fallback = null, \n  loading = null,\n  redirectTo,\n  requireRole\n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, user } = useAuth();\n\n  // Show loading state\n  if (isLoading) {\n    return loading ? <>{loading}</> : (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Handle unauthenticated users\n  if (!isAuthenticated) {\n    if (redirectTo && typeof window !== 'undefined') {\n      window.location.href = redirectTo;\n      return null;\n    }\n    return fallback ? <>{fallback}</> : null;\n  }\n\n  // Handle role-based access\n  if (requireRole && user) {\n    const userRole = (user as any).role || 'user';\n    const allowedRoles = Array.isArray(requireRole) ? requireRole : [requireRole];\n\n    if (!allowedRoles.includes(userRole)) {\n      return (\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n              Access Denied\n            </h2>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              You don't have permission to access this page.\n            </p>\n          </div>\n        </div>\n      );\n    }\n  }\n\n  return <>{children}</>;\n}\n\n/**\n * RoleGuard Component\n * \n * Renders children only if user has the required role(s).\n * \n * @example\n * <RoleGuard roles={['admin', 'moderator']}>\n *   <AdminPanel />\n * </RoleGuard>\n */\ninterface RoleGuardProps {\n  children: ReactNode;\n  roles: string | string[];\n  fallback?: ReactNode;\n}\n\nexport function RoleGuard({ children, roles, fallback = null }: RoleGuardProps) {\n  const { user, isAuthenticated, isLoading } = useAuth();\n\n  if (isLoading || !isAuthenticated || !user) {\n    return null;\n  }\n\n  const userRole = (user as any).role || 'user';\n  const allowedRoles = Array.isArray(roles) ? roles : [roles];\n\n  if (allowedRoles.includes(userRole)) {\n    return <>{children}</>;\n  }\n\n  return fallback ? <>{fallback}</> : null;\n}\n\n/**\n * UserInfo Component\n *\n * A simple component that displays basic user information.\n * For more complex use cases, use the useAuth hook directly.\n *\n * @example\n * <UserInfo />\n */\nexport function UserInfo() {\n  const { user, isAuthenticated, isLoading } = useAuth();\n\n  if (isLoading) {\n    return <span className=\"text-sm text-gray-500\">Loading...</span>;\n  }\n\n  if (!isAuthenticated || !user) {\n    return <span className=\"text-sm text-gray-500\">Not signed in</span>;\n  }\n\n  return (\n    <span className=\"text-sm text-gray-600 dark:text-gray-300\">\n      {user.name || user.email}\n    </span>\n  );\n}\n", "type": "registry:component"}, {"path": "components/ui/user-profile.tsx", "content": "\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { LogOut, User, Mail, Calendar, Shield } from \"lucide-react\";\n\ninterface UserProfileProps {\n  onSignOut?: () => void;\n  className?: string;\n}\n\nexport function UserProfile({ onSignOut, className }: UserProfileProps) {\n  const { user, signOut, isLoading } = useAuth();\n\n  if (!user) {\n    return null;\n  }\n\n  const handleSignOut = async () => {\n    await signOut();\n    onSignOut?.();\n  };\n\n  const getInitials = (name?: string) => {\n    if (!name) return \"U\";\n    return name\n      .split(\" \")\n      .map((n) => n[0])\n      .join(\"\")\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const formatDate = (date: string | Date) => {\n    return new Date(date).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n    });\n  };\n\n  return (\n    <Card className={className}>\n      <CardHeader className=\"pb-4\">\n        <div className=\"flex items-center space-x-4\">\n          <Avatar className=\"h-16 w-16\">\n            <AvatarImage src={(user as any).avatar || user.image || undefined} alt={user.name || \"User\"} />\n            <AvatarFallback className=\"text-lg\">\n              {getInitials(user.name)}\n            </AvatarFallback>\n          </Avatar>\n          <div className=\"flex-1\">\n            <CardTitle className=\"text-xl\">{user.name || \"User\"}</CardTitle>\n            <CardDescription className=\"flex items-center gap-1\">\n              <Mail className=\"h-3 w-3\" />\n              {user.email}\n            </CardDescription>\n          </div>\n          <Badge variant={(user as any).role === 'admin' ? 'default' : 'secondary'} className=\"capitalize\">\n            <Shield className=\"h-3 w-3 mr-1\" />\n            {(user as any).role || 'user'}\n          </Badge>\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-4\">\n        {/* User Information */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n              <User className=\"h-4 w-4\" />\n              <span>User ID</span>\n            </div>\n            <p className=\"text-sm font-mono bg-muted px-2 py-1 rounded\">\n              {user.id}\n            </p>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n              <Calendar className=\"h-4 w-4\" />\n              <span>Member Since</span>\n            </div>\n            <p className=\"text-sm\">\n              {formatDate(user.createdAt)}\n            </p>\n          </div>\n        </div>\n\n        {/* Email Verification Status */}\n        <div className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg\">\n          <div className=\"flex items-center gap-2\">\n            <Mail className=\"h-4 w-4\" />\n            <span className=\"text-sm font-medium\">Email Status</span>\n          </div>\n          <Badge variant={user.emailVerified ? 'default' : 'destructive'}>\n            {user.emailVerified ? 'Verified' : 'Unverified'}\n          </Badge>\n        </div>\n\n        {/* Actions */}\n        <div className=\"pt-4 border-t\">\n          <Button\n            variant=\"outline\"\n            className=\"w-full\"\n            onClick={handleSignOut}\n            disabled={isLoading}\n          >\n            <LogOut className=\"mr-2 h-4 w-4\" />\n            Sign Out\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n", "type": "registry:component"}, {"path": "actions/auth-actions.ts", "content": "\"use server\";\n\n// Define a more specific user type for auth actions\ninterface AuthUser {\n  id: string;\n  email: string;\n  name?: string;\n  role?: string;\n  emailVerified?: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\n// ============================================================================\n// AUTH SERVER ACTIONS\n// ============================================================================\n\n/**\n * Called when a user signs up for the first time\n * Customize this function with your own business logic\n */\nexport async function onUserSignup(params: {\n  user: AuthUser;\n  provider: string;\n  metadata?: Record<string, unknown>;\n  timestamp: Date;\n}) {\n  // Add your custom logic here\n  console.log('New user signup:', params);\n  \n  // Examples of what you might want to do:\n  // - Send welcome email\n  // - Create user profile\n  // - Track analytics event\n  // - Add to CRM\n  // - Set up default preferences\n  // - Create initial data\n  \n  // Example: Log the signup\n  console.log(`New user signed up: ${params.user.email} via ${params.provider}`);\n  \n  // Example: You could send a welcome email here\n  // await sendWelcomeEmail(params.user.email, params.user.name);\n  \n  // Example: Track analytics\n  // await trackEvent('user_signup', { \n  //   userId: params.user.id,\n  //   provider: params.provider,\n  //   email: params.user.email \n  // });\n  \n  // Example: Create user profile\n  // await createUserProfile(params.user.id, {\n  //   name: params.user.name,\n  //   email: params.user.email,\n  //   avatar: params.user.avatar,\n  //   role: params.user.role || 'user',\n  // });\n}\n\n/**\n * Called when a user signs in\n */\nexport async function onUserLogin(params: {\n  user: AuthUser;\n  provider: string;\n  isFirstLogin: boolean;\n  timestamp: Date;\n}) {\n  console.log('User login:', params);\n  \n  // Add your custom logic here:\n  // - Update last login timestamp\n  // - Track login analytics\n  // - Check for security alerts\n  // - Update user activity\n  // - Send login notifications (if enabled)\n  \n  // Example: Log the login\n  console.log(`User logged in: ${params.user.email} via ${params.provider}`);\n  \n  // Example: Track analytics\n  // await trackEvent('user_login', {\n  //   userId: params.user.id,\n  //   provider: params.provider,\n  //   isFirstLogin: params.isFirstLogin,\n  // });\n  \n  // Example: Update last seen\n  // await updateUserLastSeen(params.user.id, params.timestamp);\n  \n  // Example: Check for suspicious activity\n  // await checkLoginSecurity(params.user.id, {\n  //   provider: params.provider,\n  //   timestamp: params.timestamp,\n  // });\n}\n\n/**\n * Called when a user signs out\n */\nexport async function onUserLogout(params: {\n  userId: string;\n  sessionId: string;\n  timestamp: Date;\n}) {\n  console.log('User logout:', params);\n  \n  // Add your custom logic here:\n  // - Clean up session data\n  // - Track logout analytics\n  // - Update user activity\n  // - Clear temporary data\n  \n  // Example: Log the logout\n  console.log(`User logged out: ${params.userId}`);\n  \n  // Example: Track analytics\n  // await trackEvent('user_logout', {\n  //   userId: params.userId,\n  //   sessionId: params.sessionId,\n  // });\n  \n  // Example: Clean up user data\n  // await cleanupUserSession(params.sessionId);\n}\n\n/**\n * Called when a user requests a password reset\n */\nexport async function onPasswordReset(params: {\n  user: AuthUser;\n  resetToken: string;\n  timestamp: Date;\n}) {\n  console.log('Password reset requested:', params);\n  \n  // Add your custom logic here:\n  // - Send password reset email\n  // - Log security event\n  // - Track analytics\n  // - Check for abuse\n  \n  // Example: Log the password reset\n  console.log(`Password reset requested: ${params.user.email}`);\n  \n  // Example: Send password reset email\n  // await sendPasswordResetEmail(params.user.email, params.resetToken);\n  \n  // Example: Track security event\n  // await trackSecurityEvent('password_reset_requested', {\n  //   userId: params.user.id,\n  //   email: params.user.email,\n  // });\n}\n\n/**\n * Called when a user's email is verified\n */\nexport async function onEmailVerification(params: {\n  user: AuthUser;\n  email: string;\n  timestamp: Date;\n}) {\n  console.log('Email verified:', params);\n  \n  // Add your custom logic here:\n  // - Update user verification status\n  // - Send welcome email\n  // - Enable additional features\n  // - Track analytics\n  \n  // Example: Log the email verification\n  console.log(`Email verified: ${params.email}`);\n  \n  // Example: Send welcome email after verification\n  // await sendWelcomeEmail(params.email, params.user.name);\n  \n  // Example: Update user status\n  // await updateUserVerificationStatus(params.user.id, true);\n  \n  // Example: Track analytics\n  // await trackEvent('email_verified', {\n  //   userId: params.user.id,\n  //   email: params.email,\n  // });\n}\n\n/**\n * Called when a user's account is deleted\n */\nexport async function onUserAccountDeleted(params: {\n  user: AuthUser;\n  deletedBy: string; // 'user' or 'admin'\n  reason?: string;\n  timestamp: Date;\n}) {\n  console.log('User account deleted:', params);\n  \n  // Add your custom logic here:\n  // - Clean up user data\n  // - Send confirmation email\n  // - Update analytics\n  // - Archive user information\n  // - Cancel subscriptions\n  \n  // Example: Log the account deletion\n  console.log(`Account deleted: ${params.user.email} by ${params.deletedBy}`);\n  \n  // Example: Clean up user data\n  // await cleanupUserData(params.user.id);\n  \n  // Example: Send confirmation email\n  // await sendAccountDeletionConfirmation(params.user.email);\n  \n  // Example: Track analytics\n  // await trackEvent('account_deleted', {\n  //   userId: params.user.id,\n  //   deletedBy: params.deletedBy,\n  //   reason: params.reason,\n  // });\n}\n", "type": "registry:lib"}, {"path": "app/api/auth/[...all]/route.ts", "content": "/**\n * Supreme Toolkit - Better Auth API Route\n * \n * This API route handles all authentication requests for the Supreme Toolkit auth module.\n * It uses betterAuth to provide a complete authentication system.\n */\n\nimport { auth } from \"@/lib/auth\";\nimport { toNextJsHandler } from \"better-auth/next-js\";\n\n// Export the GET and POST handlers for Next.js App Router\nexport const { GET, POST } = toNextJsHandler(auth);\n", "type": "registry:lib", "target": "app/api/auth/[...all]/route.ts"}]}