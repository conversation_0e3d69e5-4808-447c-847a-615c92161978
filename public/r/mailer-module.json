{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "mailer-module", "type": "registry:component", "title": "Universal Mailer Module", "description": "Unified email system with auto-detection for Resend and Nodemailer, email templates, and testing components", "dependencies": ["resend", "nodemailer", "@react-email/components"], "registryDependencies": ["button", "input", "label", "card", "textarea"], "files": [{"path": "lib/mailer.ts", "content": "/**\n * Supreme Toolkit - Unified Mailer\n * \n * This module automatically detects and uses the appropriate mailer\n * based on what's available in the user's environment.\n * \n * Priority:\n * 1. Resend (if RESEND_API_KEY is set)\n * 2. Nodemailer (if SMTP/Gmail config is set)\n * 3. Fallback to Resend with warning\n */\n\nexport interface EmailOptions {\n  to: string | string[];\n  from?: string;\n  subject: string;\n  html?: string;\n  text?: string;\n  replyTo?: string;\n  cc?: string | string[];\n  bcc?: string | string[];\n  attachments?: Array<{\n    filename: string;\n    content: string | Buffer;\n    contentType?: string;\n  }>;\n}\n\nexport interface MailerResult {\n  success: boolean;\n  id?: string;\n  error?: string;\n}\n\n/**\n * Detect which mailer to use based on environment variables\n */\nfunction detectMailer(): 'resend' | 'nodemailer' | 'none' {\n  // Check for Resend\n  if (process.env.RESEND_API_KEY) {\n    return 'resend';\n  }\n  \n  // Check for Nodemailer configurations\n  if (\n    process.env.SMTP_HOST || \n    process.env.GMAIL_USER || \n    process.env.EMAIL_PROVIDER\n  ) {\n    return 'nodemailer';\n  }\n  \n  return 'none';\n}\n\n/**\n * Get the appropriate mailer module\n */\nasync function getMailer() {\n  const mailerType = detectMailer();\n  \n  switch (mailerType) {\n    case 'resend':\n      try {\n        const resendMailer = await import('./mailer-resend');\n        return { type: 'resend', mailer: resendMailer.default };\n      } catch {\n        console.warn('Resend mailer not available, falling back to nodemailer');\n        const nodemailerMailer = await import('./mailer-nodemailer');\n        return { type: 'nodemailer', mailer: nodemailerMailer.default };\n      }\n      \n    case 'nodemailer':\n      try {\n        const nodemailerMailer = await import('./mailer-nodemailer');\n        return { type: 'nodemailer', mailer: nodemailerMailer.default };\n      } catch {\n        console.warn('Nodemailer not available, falling back to resend');\n        const resendMailer = await import('./mailer-resend');\n        return { type: 'resend', mailer: resendMailer.default };\n      }\n      \n    case 'none':\n    default:\n      console.warn('No mailer configuration found, using Resend as default. Please set RESEND_API_KEY or SMTP configuration.');\n      try {\n        const resendMailer = await import('./mailer-resend');\n        return { type: 'resend', mailer: resendMailer.default };\n      } catch {\n        throw new Error('No mailer available. Please install and configure either Resend or Nodemailer.');\n      }\n  }\n}\n\n/**\n * Send an email using the detected mailer\n */\nexport async function sendEmail(options: EmailOptions): Promise<MailerResult> {\n  try {\n    const { mailer } = await getMailer();\n    return await mailer.sendEmail(options);\n  } catch (error) {\n    console.error('Failed to send email:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Send a welcome email to waitlist subscribers\n */\nexport async function sendWaitlistWelcomeEmail(\n  email: string, \n  name?: string, \n  position?: number\n): Promise<MailerResult> {\n  try {\n    const { mailer } = await getMailer();\n    return await mailer.sendWaitlistWelcomeEmail(email, name, position);\n  } catch (error) {\n    console.error('Failed to send waitlist welcome email:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Send waitlist approval email\n */\nexport async function sendWaitlistApprovalEmail(\n  email: string, \n  name?: string\n): Promise<MailerResult> {\n  try {\n    const { mailer } = await getMailer();\n    return await mailer.sendWaitlistApprovalEmail(email, name);\n  } catch (error) {\n    console.error('Failed to send waitlist approval email:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Test email configuration\n */\nexport async function testEmailConfiguration(): Promise<MailerResult & { mailerType?: string }> {\n  try {\n    const { type, mailer } = await getMailer();\n    const result = await mailer.testEmailConfiguration();\n    return { ...result, mailerType: type };\n  } catch (error) {\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Get mailer configuration info\n */\nexport function getMailerInfo(): { \n  type: 'resend' | 'nodemailer' | 'none';\n  configured: boolean;\n  details: string;\n} {\n  const type = detectMailer();\n  \n  switch (type) {\n    case 'resend':\n      return {\n        type: 'resend',\n        configured: true,\n        details: 'Using Resend API for email delivery'\n      };\n      \n    case 'nodemailer':\n      const provider = process.env.EMAIL_PROVIDER || 'smtp';\n      return {\n        type: 'nodemailer',\n        configured: true,\n        details: `Using Nodemailer with ${provider.toUpperCase()} configuration`\n      };\n      \n    case 'none':\n    default:\n      return {\n        type: 'none',\n        configured: false,\n        details: 'No email configuration found. Please set RESEND_API_KEY or SMTP configuration.'\n      };\n  }\n}\n\n/**\n * Send a test email to verify configuration\n */\nexport async function sendTestEmail(to: string): Promise<MailerResult> {\n  const subject = 'Supreme Toolkit - Email Configuration Test';\n  const html = `\n    <h2>Email Configuration Test</h2>\n    <p>This is a test email to verify your Supreme Toolkit email configuration.</p>\n    <p><strong>Mailer:</strong> ${getMailerInfo().details}</p>\n    <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>\n    <p>If you received this email, your configuration is working correctly! 🎉</p>\n  `;\n  \n  const text = `\n    Email Configuration Test\n    \n    This is a test email to verify your Supreme Toolkit email configuration.\n    \n    Mailer: ${getMailerInfo().details}\n    Timestamp: ${new Date().toISOString()}\n    \n    If you received this email, your configuration is working correctly!\n  `;\n\n  return await sendEmail({\n    to,\n    subject,\n    html,\n    text,\n  });\n}\n\nexport default {\n  sendEmail,\n  sendWaitlistWelcomeEmail,\n  sendWaitlistApprovalEmail,\n  testEmailConfiguration,\n  getMailerInfo,\n  sendTestEmail,\n};\n", "type": "registry:lib"}, {"path": "lib/mailer-resend.ts", "content": "/**\n * Supreme Toolkit - Resend Mailer\n * \n * This module provides email functionality using Resend.\n * Resend is a modern email API that's easy to use and reliable.\n */\n\nimport { Resend } from 'resend';\n\n// Initialize Resend client\nconst resend = new Resend(process.env.RESEND_API_KEY);\n\nexport interface EmailOptions {\n  to: string | string[];\n  from?: string;\n  subject: string;\n  html?: string;\n  text?: string;\n  replyTo?: string;\n  cc?: string | string[];\n  bcc?: string | string[];\n  attachments?: Array<{\n    filename: string;\n    content: string | Buffer;\n    contentType?: string;\n  }>;\n}\n\nexport interface EmailTemplate {\n  name: string;\n  subject: string;\n  html: string;\n  text?: string;\n}\n\n/**\n * Send an email using Resend\n */\nexport async function sendEmail(options: EmailOptions): Promise<{ success: boolean; id?: string; error?: string }> {\n  try {\n    if (!process.env.RESEND_API_KEY) {\n      throw new Error('RESEND_API_KEY environment variable is not set');\n    }\n\n    const fromEmail = options.from || process.env.RESEND_FROM_EMAIL || '<EMAIL>';\n\n    const emailData: any = {\n      from: fromEmail,\n      to: Array.isArray(options.to) ? options.to : [options.to],\n      subject: options.subject,\n      text: options.text || options.html || 'No content provided',\n    };\n\n    if (options.html) emailData.html = options.html;\n    if (options.replyTo) emailData.replyTo = options.replyTo;\n    if (options.cc) emailData.cc = options.cc;\n    if (options.bcc) emailData.bcc = options.bcc;\n    if (options.attachments) emailData.attachments = options.attachments;\n\n    const result = await resend.emails.send(emailData);\n\n    if (result.error) {\n      console.error('Resend error:', result.error);\n      return { success: false, error: result.error.message };\n    }\n\n    return { success: true, id: result.data?.id };\n  } catch (error) {\n    console.error('Email sending failed:', error);\n    return { \n      success: false, \n      error: error instanceof Error ? error.message : 'Unknown error occurred' \n    };\n  }\n}\n\n/**\n * Send a welcome email to waitlist subscribers\n */\nexport async function sendWaitlistWelcomeEmail(\n  email: string, \n  name?: string, \n  position?: number\n): Promise<{ success: boolean; error?: string }> {\n  const subject = 'Welcome to our waitlist! 🎉';\n  \n  const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>Welcome to our waitlist</title>\n      <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .content { background: #f9f9f9; padding: 30px; border-radius: 8px; }\n        .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }\n        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🎉 Welcome to our waitlist!</h1>\n        </div>\n        \n        <div class=\"content\">\n          <p>Hi ${name || 'there'},</p>\n          \n          <p>Thank you for joining our waitlist! We're excited to have you on board.</p>\n          \n          ${position ? `<p><strong>Your position:</strong> #${position}</p>` : ''}\n          \n          <p>We'll keep you updated on our progress and let you know as soon as we're ready to welcome you.</p>\n          \n          <p>In the meantime, feel free to:</p>\n          <ul>\n            <li>Follow us on social media for updates</li>\n            <li>Share with friends to move up in the waitlist</li>\n            <li>Reply to this email if you have any questions</li>\n          </ul>\n          \n          <p>Thanks again for your interest!</p>\n          <p>The Team</p>\n        </div>\n        \n        <div class=\"footer\">\n          <p>You're receiving this email because you signed up for our waitlist.</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n\n  const text = `\n    Welcome to our waitlist!\n    \n    Hi ${name || 'there'},\n    \n    Thank you for joining our waitlist! We're excited to have you on board.\n    \n    ${position ? `Your position: #${position}` : ''}\n    \n    We'll keep you updated on our progress and let you know as soon as we're ready to welcome you.\n    \n    Thanks again for your interest!\n    The Team\n  `;\n\n  return await sendEmail({\n    to: email,\n    subject,\n    html,\n    text,\n  });\n}\n\n/**\n * Send waitlist approval email\n */\nexport async function sendWaitlistApprovalEmail(\n  email: string, \n  name?: string\n): Promise<{ success: boolean; error?: string }> {\n  const subject = 'You\\'re approved! Welcome aboard! 🚀';\n  \n  const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>You're approved!</title>\n      <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .content { background: #f0f9ff; padding: 30px; border-radius: 8px; border: 2px solid #0ea5e9; }\n        .button { display: inline-block; background: #0ea5e9; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }\n        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🚀 You're approved!</h1>\n        </div>\n        \n        <div class=\"content\">\n          <p>Hi ${name || 'there'},</p>\n          \n          <p><strong>Great news!</strong> You've been approved and can now access our platform.</p>\n          \n          <p>We're thrilled to welcome you aboard and can't wait to see what you'll create.</p>\n          \n          <div style=\"text-align: center;\">\n            <a href=\"${process.env.NEXT_PUBLIC_APP_URL || 'https://yourapp.com'}\" class=\"button\">\n              Get Started Now\n            </a>\n          </div>\n          \n          <p>If you have any questions or need help getting started, don't hesitate to reach out.</p>\n          \n          <p>Welcome to the community!</p>\n          <p>The Team</p>\n        </div>\n        \n        <div class=\"footer\">\n          <p>You're receiving this email because you were approved from our waitlist.</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n\n  const text = `\n    You're approved!\n    \n    Hi ${name || 'there'},\n    \n    Great news! You've been approved and can now access our platform.\n    \n    We're thrilled to welcome you aboard and can't wait to see what you'll create.\n    \n    Get started: ${process.env.NEXT_PUBLIC_APP_URL || 'https://yourapp.com'}\n    \n    If you have any questions or need help getting started, don't hesitate to reach out.\n    \n    Welcome to the community!\n    The Team\n  `;\n\n  return await sendEmail({\n    to: email,\n    subject,\n    html,\n    text,\n  });\n}\n\n/**\n * Test email configuration\n */\nexport async function testEmailConfiguration(): Promise<{ success: boolean; error?: string }> {\n  try {\n    if (!process.env.RESEND_API_KEY) {\n      return { success: false, error: 'RESEND_API_KEY environment variable is not set' };\n    }\n\n    // Test with a simple email\n    const result = await sendEmail({\n      to: '<EMAIL>',\n      subject: 'Test Email Configuration',\n      text: 'This is a test email to verify Resend configuration.',\n      html: '<p>This is a test email to verify Resend configuration.</p>',\n    });\n\n    return result;\n  } catch (error) {\n    return { \n      success: false, \n      error: error instanceof Error ? error.message : 'Unknown error occurred' \n    };\n  }\n}\n\nexport default {\n  sendEmail,\n  sendWaitlistWelcomeEmail,\n  sendWaitlistApprovalEmail,\n  testEmailConfiguration,\n};\n", "type": "registry:lib"}, {"path": "lib/mailer-nodemailer.ts", "content": "/**\n * Supreme Toolkit - Nodemailer\n * \n * This module provides email functionality using Nodemailer.\n * Supports SMTP, Gmail, and other email providers.\n */\n\nimport nodemailer from 'nodemailer';\nimport type { Transporter } from 'nodemailer';\n\nexport interface EmailOptions {\n  to: string | string[];\n  from?: string;\n  subject: string;\n  html?: string;\n  text?: string;\n  replyTo?: string;\n  cc?: string | string[];\n  bcc?: string | string[];\n  attachments?: Array<{\n    filename: string;\n    content: string | Buffer;\n    contentType?: string;\n  }>;\n}\n\n// Create transporter based on environment configuration\nfunction createTransporter(): Transporter {\n  const emailProvider = process.env.EMAIL_PROVIDER || 'smtp';\n\n  switch (emailProvider.toLowerCase()) {\n    case 'gmail':\n      return nodemailer.createTransport({\n        service: 'gmail',\n        auth: {\n          user: process.env.GMAIL_USER,\n          pass: process.env.GMAIL_APP_PASSWORD, // Use App Password, not regular password\n        },\n      });\n\n    case 'smtp':\n    default:\n      return nodemailer.createTransport({\n        host: process.env.SMTP_HOST || 'localhost',\n        port: parseInt(process.env.SMTP_PORT || '587'),\n        secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports\n        auth: {\n          user: process.env.SMTP_USER,\n          pass: process.env.SMTP_PASSWORD,\n        },\n        tls: {\n          rejectUnauthorized: process.env.SMTP_REJECT_UNAUTHORIZED !== 'false',\n        },\n      });\n  }\n}\n\n/**\n * Send an email using Nodemailer\n */\nexport async function sendEmail(options: EmailOptions): Promise<{ success: boolean; id?: string; error?: string }> {\n  try {\n    const transporter = createTransporter();\n    \n    const fromEmail = options.from || process.env.EMAIL_FROM || '<EMAIL>';\n\n    const mailOptions = {\n      from: fromEmail,\n      to: Array.isArray(options.to) ? options.to.join(', ') : options.to,\n      subject: options.subject,\n      html: options.html,\n      text: options.text,\n      replyTo: options.replyTo,\n      cc: Array.isArray(options.cc) ? options.cc.join(', ') : options.cc,\n      bcc: Array.isArray(options.bcc) ? options.bcc.join(', ') : options.bcc,\n      attachments: options.attachments,\n    };\n\n    const result = await transporter.sendMail(mailOptions);\n    \n    return { success: true, id: result.messageId };\n  } catch (error) {\n    console.error('Email sending failed:', error);\n    return { \n      success: false, \n      error: error instanceof Error ? error.message : 'Unknown error occurred' \n    };\n  }\n}\n\n/**\n * Send a welcome email to waitlist subscribers\n */\nexport async function sendWaitlistWelcomeEmail(\n  email: string, \n  name?: string, \n  position?: number\n): Promise<{ success: boolean; error?: string }> {\n  const subject = 'Welcome to our waitlist! 🎉';\n  \n  const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>Welcome to our waitlist</title>\n      <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .content { background: #f9f9f9; padding: 30px; border-radius: 8px; }\n        .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }\n        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🎉 Welcome to our waitlist!</h1>\n        </div>\n        \n        <div class=\"content\">\n          <p>Hi ${name || 'there'},</p>\n          \n          <p>Thank you for joining our waitlist! We're excited to have you on board.</p>\n          \n          ${position ? `<p><strong>Your position:</strong> #${position}</p>` : ''}\n          \n          <p>We'll keep you updated on our progress and let you know as soon as we're ready to welcome you.</p>\n          \n          <p>In the meantime, feel free to:</p>\n          <ul>\n            <li>Follow us on social media for updates</li>\n            <li>Share with friends to move up in the waitlist</li>\n            <li>Reply to this email if you have any questions</li>\n          </ul>\n          \n          <p>Thanks again for your interest!</p>\n          <p>The Team</p>\n        </div>\n        \n        <div class=\"footer\">\n          <p>You're receiving this email because you signed up for our waitlist.</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n\n  const text = `\n    Welcome to our waitlist!\n    \n    Hi ${name || 'there'},\n    \n    Thank you for joining our waitlist! We're excited to have you on board.\n    \n    ${position ? `Your position: #${position}` : ''}\n    \n    We'll keep you updated on our progress and let you know as soon as we're ready to welcome you.\n    \n    Thanks again for your interest!\n    The Team\n  `;\n\n  return await sendEmail({\n    to: email,\n    subject,\n    html,\n    text,\n  });\n}\n\n/**\n * Send waitlist approval email\n */\nexport async function sendWaitlistApprovalEmail(\n  email: string, \n  name?: string\n): Promise<{ success: boolean; error?: string }> {\n  const subject = 'You\\'re approved! Welcome aboard! 🚀';\n  \n  const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>You're approved!</title>\n      <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .content { background: #f0f9ff; padding: 30px; border-radius: 8px; border: 2px solid #0ea5e9; }\n        .button { display: inline-block; background: #0ea5e9; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }\n        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🚀 You're approved!</h1>\n        </div>\n        \n        <div class=\"content\">\n          <p>Hi ${name || 'there'},</p>\n          \n          <p><strong>Great news!</strong> You've been approved and can now access our platform.</p>\n          \n          <p>We're thrilled to welcome you aboard and can't wait to see what you'll create.</p>\n          \n          <div style=\"text-align: center;\">\n            <a href=\"${process.env.NEXT_PUBLIC_APP_URL || 'https://yourapp.com'}\" class=\"button\">\n              Get Started Now\n            </a>\n          </div>\n          \n          <p>If you have any questions or need help getting started, don't hesitate to reach out.</p>\n          \n          <p>Welcome to the community!</p>\n          <p>The Team</p>\n        </div>\n        \n        <div class=\"footer\">\n          <p>You're receiving this email because you were approved from our waitlist.</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n\n  const text = `\n    You're approved!\n    \n    Hi ${name || 'there'},\n    \n    Great news! You've been approved and can now access our platform.\n    \n    We're thrilled to welcome you aboard and can't wait to see what you'll create.\n    \n    Get started: ${process.env.NEXT_PUBLIC_APP_URL || 'https://yourapp.com'}\n    \n    If you have any questions or need help getting started, don't hesitate to reach out.\n    \n    Welcome to the community!\n    The Team\n  `;\n\n  return await sendEmail({\n    to: email,\n    subject,\n    html,\n    text,\n  });\n}\n\n/**\n * Test email configuration\n */\nexport async function testEmailConfiguration(): Promise<{ success: boolean; error?: string }> {\n  try {\n    const transporter = createTransporter();\n    \n    // Verify the connection\n    await transporter.verify();\n    \n    return { success: true };\n  } catch (error) {\n    return { \n      success: false, \n      error: error instanceof Error ? error.message : 'Unknown error occurred' \n    };\n  }\n}\n\nexport default {\n  sendEmail,\n  sendWaitlistWelcomeEmail,\n  sendWaitlistApprovalEmail,\n  testEmailConfiguration,\n};\n", "type": "registry:lib"}, {"path": "hooks/use-mailer.ts", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { sendEmail, getMailerInfo, sendTestEmail } from \"@/lib/mailer\";\nimport { onEmailSent, onEmailDelivered, onEmailFailed } from \"@/actions/mailer-actions\";\n\nexport interface EmailOptions {\n  to: string | string[];\n  from?: string;\n  subject: string;\n  html?: string;\n  text?: string;\n  replyTo?: string;\n  cc?: string | string[];\n  bcc?: string | string[];\n  attachments?: Array<{\n    filename: string;\n    content: string | Buffer;\n    contentType?: string;\n  }>;\n}\n\ninterface UseMailerOptions {\n  onSuccess?: (data: any) => void;\n  onError?: (error: string) => void;\n}\n\nexport function useMailer(options: UseMailerOptions = {}) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [lastResult, setLastResult] = useState<any>(null);\n\n  const send = async (emailOptions: EmailOptions) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Call server action before sending\n      await onEmailSent({\n        to: emailOptions.to,\n        subject: emailOptions.subject,\n        timestamp: new Date(),\n        metadata: {\n          from: emailOptions.from,\n          hasAttachments: !!emailOptions.attachments?.length,\n          recipientCount: Array.isArray(emailOptions.to) ? emailOptions.to.length : 1,\n        },\n      });\n\n      const result = await sendEmail(emailOptions);\n\n      if (result.success) {\n        // Call success server action\n        await onEmailDelivered({\n          to: emailOptions.to,\n          subject: emailOptions.subject,\n          messageId: result.id,\n          timestamp: new Date(),\n          metadata: {\n            from: emailOptions.from,\n            mailerType: getMailerInfo().type,\n          },\n        });\n\n        setLastResult(result);\n        options.onSuccess?.(result);\n        return { success: true, data: result };\n      } else {\n        throw new Error(result.error || 'Failed to send email');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      \n      // Call error server action\n      await onEmailFailed({\n        to: emailOptions.to,\n        subject: emailOptions.subject,\n        error: errorMessage,\n        timestamp: new Date(),\n        metadata: {\n          from: emailOptions.from,\n          mailerType: getMailerInfo().type,\n        },\n      });\n\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const sendTest = async (to: string) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await sendTestEmail(to);\n\n      if (result.success) {\n        setLastResult(result);\n        options.onSuccess?.(result);\n        return { success: true, data: result };\n      } else {\n        throw new Error(result.error || 'Failed to send test email');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getInfo = () => {\n    return getMailerInfo();\n  };\n\n  return {\n    // State\n    isLoading,\n    error,\n    lastResult,\n    \n    // Methods\n    send,\n    sendTest,\n    getInfo,\n    \n    // Mailer info\n    mailerInfo: getMailerInfo(),\n  };\n}\n", "type": "registry:hook"}, {"path": "components/ui/email-templates.tsx", "content": "/**\n * Supreme Toolkit - Email Templates\n * \n * Reusable email template components for consistent email design.\n * These components generate HTML strings for email content.\n */\n\nexport interface EmailTemplateProps {\n  [key: string]: any;\n}\n\n/**\n * Base Email Template\n * Provides consistent styling and structure for all emails\n */\nexport function BaseEmailTemplate({\n  title,\n  children,\n  footerText = \"You're receiving this email because you signed up for our service.\",\n  unsubscribeUrl,\n  companyName = \"Your Company\",\n  _companyUrl = process.env.NEXT_PUBLIC_APP_URL || \"https://yourcompany.com\",\n}: {\n  title: string;\n  children: string;\n  footerText?: string;\n  unsubscribeUrl?: string;\n  companyName?: string;\n  _companyUrl?: string;\n}) {\n  return `\n    <!DOCTYPE html>\n    <html lang=\"en\">\n    <head>\n      <meta charset=\"utf-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${title}</title>\n      <style>\n        body {\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;\n          line-height: 1.6;\n          color: #333333;\n          margin: 0;\n          padding: 0;\n          background-color: #f4f4f4;\n        }\n        .container {\n          max-width: 600px;\n          margin: 0 auto;\n          background-color: #ffffff;\n          padding: 0;\n        }\n        .header {\n          background-color: #ffffff;\n          padding: 30px 40px 20px;\n          text-align: center;\n          border-bottom: 1px solid #e5e5e5;\n        }\n        .content {\n          padding: 40px;\n        }\n        .footer {\n          background-color: #f8f9fa;\n          padding: 30px 40px;\n          text-align: center;\n          color: #666666;\n          font-size: 14px;\n          border-top: 1px solid #e5e5e5;\n        }\n        .button {\n          display: inline-block;\n          background-color: #007bff;\n          color: #ffffff;\n          padding: 12px 24px;\n          text-decoration: none;\n          border-radius: 6px;\n          font-weight: 500;\n          margin: 20px 0;\n        }\n        .button:hover {\n          background-color: #0056b3;\n        }\n        h1 {\n          color: #333333;\n          font-size: 24px;\n          margin: 0 0 20px;\n        }\n        h2 {\n          color: #333333;\n          font-size: 20px;\n          margin: 30px 0 15px;\n        }\n        p {\n          margin: 0 0 15px;\n        }\n        .highlight {\n          background-color: #f8f9fa;\n          padding: 20px;\n          border-radius: 6px;\n          border-left: 4px solid #007bff;\n          margin: 20px 0;\n        }\n        .text-center {\n          text-align: center;\n        }\n        .text-muted {\n          color: #666666;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>${companyName}</h1>\n        </div>\n        \n        <div class=\"content\">\n          ${children}\n        </div>\n        \n        <div class=\"footer\">\n          <p>${footerText}</p>\n          ${unsubscribeUrl ? `<p><a href=\"${unsubscribeUrl}\" style=\"color: #666666;\">Unsubscribe</a></p>` : ''}\n          <p>&copy; ${new Date().getFullYear()} ${companyName}. All rights reserved.</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n}\n\n/**\n * Welcome Email Template\n */\nexport function WelcomeEmailTemplate({\n  name,\n  ctaUrl,\n  ctaText = \"Get Started\",\n  companyName = \"Your Company\",\n}: {\n  name?: string;\n  ctaUrl?: string;\n  ctaText?: string;\n  companyName?: string;\n}) {\n  const content = `\n    <h1>Welcome${name ? `, ${name}` : ''}! 🎉</h1>\n    \n    <p>Thank you for joining ${companyName}! We're excited to have you on board.</p>\n    \n    <p>Here's what you can do next:</p>\n    \n    <ul>\n      <li>Complete your profile setup</li>\n      <li>Explore our features</li>\n      <li>Connect with our community</li>\n    </ul>\n    \n    ${ctaUrl ? `\n      <div class=\"text-center\">\n        <a href=\"${ctaUrl}\" class=\"button\">${ctaText}</a>\n      </div>\n    ` : ''}\n    \n    <p>If you have any questions or need help getting started, don't hesitate to reach out to our support team.</p>\n    \n    <p>Welcome to the community!</p>\n    \n    <p>Best regards,<br>The ${companyName} Team</p>\n  `;\n\n  return BaseEmailTemplate({\n    title: `Welcome to ${companyName}`,\n    children: content,\n    companyName,\n  });\n}\n\n/**\n * Password Reset Email Template\n */\nexport function PasswordResetEmailTemplate({\n  name,\n  resetUrl,\n  expiryHours = 24,\n  companyName = \"Your Company\",\n}: {\n  name?: string;\n  resetUrl: string;\n  expiryHours?: number;\n  companyName?: string;\n}) {\n  const content = `\n    <h1>Password Reset Request</h1>\n    \n    <p>Hi${name ? ` ${name}` : ''},</p>\n    \n    <p>We received a request to reset your password for your ${companyName} account.</p>\n    \n    <div class=\"highlight\">\n      <p><strong>If you didn't request this password reset, please ignore this email.</strong></p>\n    </div>\n    \n    <p>To reset your password, click the button below:</p>\n    \n    <div class=\"text-center\">\n      <a href=\"${resetUrl}\" class=\"button\">Reset Password</a>\n    </div>\n    \n    <p>This link will expire in ${expiryHours} hours for security reasons.</p>\n    \n    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>\n    <p style=\"word-break: break-all; color: #666666;\">${resetUrl}</p>\n    \n    <p>Best regards,<br>The ${companyName} Team</p>\n  `;\n\n  return BaseEmailTemplate({\n    title: \"Password Reset Request\",\n    children: content,\n    companyName,\n  });\n}\n\n/**\n * Email Verification Template\n */\nexport function EmailVerificationTemplate({\n  name,\n  verificationUrl,\n  companyName = \"Your Company\",\n}: {\n  name?: string;\n  verificationUrl: string;\n  companyName?: string;\n}) {\n  const content = `\n    <h1>Verify Your Email Address</h1>\n    \n    <p>Hi${name ? ` ${name}` : ''},</p>\n    \n    <p>Thank you for signing up for ${companyName}! To complete your registration, please verify your email address.</p>\n    \n    <div class=\"text-center\">\n      <a href=\"${verificationUrl}\" class=\"button\">Verify Email Address</a>\n    </div>\n    \n    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>\n    <p style=\"word-break: break-all; color: #666666;\">${verificationUrl}</p>\n    \n    <div class=\"highlight\">\n      <p><strong>This verification link will expire in 24 hours.</strong></p>\n    </div>\n    \n    <p>If you didn't create an account with ${companyName}, please ignore this email.</p>\n    \n    <p>Best regards,<br>The ${companyName} Team</p>\n  `;\n\n  return BaseEmailTemplate({\n    title: \"Verify Your Email Address\",\n    children: content,\n    companyName,\n  });\n}\n\n/**\n * Notification Email Template\n */\nexport function NotificationEmailTemplate({\n  title,\n  message,\n  ctaUrl,\n  ctaText,\n  priority = \"normal\",\n  companyName = \"Your Company\",\n}: {\n  title: string;\n  message: string;\n  ctaUrl?: string;\n  ctaText?: string;\n  priority?: \"low\" | \"normal\" | \"high\";\n  companyName?: string;\n}) {\n  const priorityColors = {\n    low: \"#28a745\",\n    normal: \"#007bff\",\n    high: \"#dc3545\",\n  };\n\n  const content = `\n    <h1>${title}</h1>\n    \n    <div class=\"highlight\" style=\"border-left-color: ${priorityColors[priority]};\">\n      <p>${message}</p>\n    </div>\n    \n    ${ctaUrl && ctaText ? `\n      <div class=\"text-center\">\n        <a href=\"${ctaUrl}\" class=\"button\" style=\"background-color: ${priorityColors[priority]};\">${ctaText}</a>\n      </div>\n    ` : ''}\n    \n    <p>Best regards,<br>The ${companyName} Team</p>\n  `;\n\n  return BaseEmailTemplate({\n    title,\n    children: content,\n    companyName,\n  });\n}\n", "type": "registry:component"}, {"path": "components/ui/mailer-test.tsx", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { useMailer } from \"@/hooks/use-mailer\";\nimport { Loader2, Mail, CheckCircle, XCircle, Info } from \"lucide-react\";\n\ninterface MailerTestProps {\n  className?: string;\n}\n\nexport function MailerTest({ className }: MailerTestProps) {\n  const [email, setEmail] = useState(\"\");\n  const [subject, setSubject] = useState(\"Test Email from Supreme Toolkit\");\n  const [message, setMessage] = useState(\"This is a test email to verify your mailer configuration.\");\n  const [testResult, setTestResult] = useState<any>(null);\n\n  const { send, sendTest, isLoading, error, mailerInfo } = useMailer({\n    onSuccess: (data) => {\n      setTestResult({ success: true, data });\n    },\n    onError: (error) => {\n      setTestResult({ success: false, error });\n    },\n  });\n\n  const handleSendTest = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email) return;\n    \n    setTestResult(null);\n    await sendTest(email);\n  };\n\n  const handleSendCustom = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email || !subject || !message) return;\n    \n    setTestResult(null);\n    await send({\n      to: email,\n      subject,\n      text: message,\n      html: `<p>${message.replace(/\\n/g, '<br>')}</p>`,\n    });\n  };\n\n  const getStatusIcon = () => {\n    if (testResult?.success) {\n      return <CheckCircle className=\"h-5 w-5 text-green-600\" />;\n    } else if (testResult?.success === false) {\n      return <XCircle className=\"h-5 w-5 text-red-600\" />;\n    }\n    return <Info className=\"h-5 w-5 text-blue-600\" />;\n  };\n\n  const getStatusColor = () => {\n    if (testResult?.success) return \"text-green-600\";\n    if (testResult?.success === false) return \"text-red-600\";\n    return \"text-blue-600\";\n  };\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Mail className=\"h-5 w-5\" />\n          Email Configuration Test\n        </CardTitle>\n        <CardDescription>\n          Test your email configuration and send test emails\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-6\">\n        {/* Mailer Info */}\n        <div className=\"space-y-3\">\n          <h3 className=\"text-sm font-medium\">Current Configuration</h3>\n          <div className=\"flex items-center justify-between p-3 bg-muted rounded-lg\">\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm font-medium\">Mailer Type:</span>\n              <Badge variant={mailerInfo.configured ? \"default\" : \"destructive\"}>\n                {mailerInfo.type.toUpperCase()}\n              </Badge>\n            </div>\n            <div className=\"text-sm text-muted-foreground\">\n              {mailerInfo.configured ? \"✓ Configured\" : \"⚠ Not Configured\"}\n            </div>\n          </div>\n          <p className=\"text-sm text-muted-foreground\">\n            {mailerInfo.details}\n          </p>\n        </div>\n\n        <Separator />\n\n        {/* Quick Test */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-sm font-medium\">Quick Test</h3>\n          <form onSubmit={handleSendTest} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"test-email\">Test Email Address</Label>\n              <Input\n                id=\"test-email\"\n                type=\"email\"\n                placeholder=\"Enter your email address\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            \n            <Button type=\"submit\" disabled={isLoading || !email} className=\"w-full\">\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Sending Test Email...\n                </>\n              ) : (\n                <>\n                  <Mail className=\"mr-2 h-4 w-4\" />\n                  Send Test Email\n                </>\n              )}\n            </Button>\n          </form>\n        </div>\n\n        <Separator />\n\n        {/* Custom Email */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-sm font-medium\">Custom Email</h3>\n          <form onSubmit={handleSendCustom} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"custom-email\">Recipient Email</Label>\n              <Input\n                id=\"custom-email\"\n                type=\"email\"\n                placeholder=\"Enter recipient email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"custom-subject\">Subject</Label>\n              <Input\n                id=\"custom-subject\"\n                type=\"text\"\n                placeholder=\"Enter email subject\"\n                value={subject}\n                onChange={(e) => setSubject(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"custom-message\">Message</Label>\n              <Textarea\n                id=\"custom-message\"\n                placeholder=\"Enter your message\"\n                value={message}\n                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setMessage(e.target.value)}\n                required\n                disabled={isLoading}\n                rows={4}\n              />\n            </div>\n            \n            <Button \n              type=\"submit\" \n              disabled={isLoading || !email || !subject || !message} \n              className=\"w-full\"\n            >\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Sending Email...\n                </>\n              ) : (\n                <>\n                  <Mail className=\"mr-2 h-4 w-4\" />\n                  Send Custom Email\n                </>\n              )}\n            </Button>\n          </form>\n        </div>\n\n        {/* Results */}\n        {(testResult || error) && (\n          <>\n            <Separator />\n            <div className=\"space-y-3\">\n              <h3 className=\"text-sm font-medium\">Result</h3>\n              <div className={`flex items-start gap-3 p-4 rounded-lg border ${\n                testResult?.success \n                  ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' \n                  : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'\n              }`}>\n                {getStatusIcon()}\n                <div className=\"flex-1 space-y-1\">\n                  <p className={`text-sm font-medium ${getStatusColor()}`}>\n                    {testResult?.success ? 'Email sent successfully!' : 'Email sending failed'}\n                  </p>\n                  {testResult?.success && testResult.data?.id && (\n                    <p className=\"text-xs text-muted-foreground\">\n                      Message ID: {testResult.data.id}\n                    </p>\n                  )}\n                  {(testResult?.error || error) && (\n                    <p className=\"text-xs text-red-600\">\n                      {testResult?.error || error}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </>\n        )}\n\n        {/* Help */}\n        <div className=\"text-xs text-muted-foreground space-y-1\">\n          <p><strong>Configuration Help:</strong></p>\n          <p>• For Resend: Set RESEND_API_KEY in your environment variables</p>\n          <p>• For SMTP: Set SMTP_HOST, SMTP_USER, SMTP_PASSWORD</p>\n          <p>• For Gmail: Set EMAIL_PROVIDER=gmail, GMAIL_USER, GMAIL_APP_PASSWORD</p>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n", "type": "registry:component"}, {"path": "actions/mailer-actions.ts", "content": "\"use server\";\n\n// ============================================================================\n// MAILER SERVER ACTIONS\n// ============================================================================\n\n/**\n * Called when an email is about to be sent\n * Customize this function with your own business logic\n */\nexport async function onEmailSent(params: {\n  to: string | string[];\n  subject: string;\n  timestamp: Date;\n  metadata?: {\n    from?: string;\n    hasAttachments?: boolean;\n    recipientCount?: number;\n    [key: string]: any;\n  };\n}) {\n  // Add your custom logic here\n  console.log('Email sending initiated:', params);\n  \n  // Examples of what you might want to do:\n  // - Log email attempts\n  // - Track analytics\n  // - Rate limiting checks\n  // - Spam prevention\n  // - User quota checks\n  // - Audit logging\n  \n  // Example: Log the email attempt\n  const recipients = Array.isArray(params.to) ? params.to.join(', ') : params.to;\n  console.log(`Sending email to: ${recipients} - Subject: ${params.subject}`);\n  \n  // Example: Track analytics\n  // await trackEvent('email_sent_attempt', {\n  //   recipients: params.metadata?.recipientCount || 1,\n  //   subject: params.subject,\n  //   hasAttachments: params.metadata?.hasAttachments || false,\n  //   timestamp: params.timestamp,\n  // });\n  \n  // Example: Check user quota\n  // const userQuota = await checkUserEmailQuota(params.metadata?.from);\n  // if (userQuota.exceeded) {\n  //   throw new Error('Email quota exceeded');\n  // }\n  \n  // Example: Spam prevention\n  // await checkSpamScore(params.subject, params.to);\n  \n  return { success: true };\n}\n\n/**\n * Called when an email is successfully delivered\n */\nexport async function onEmailDelivered(params: {\n  to: string | string[];\n  subject: string;\n  messageId?: string;\n  timestamp: Date;\n  metadata?: {\n    from?: string;\n    mailerType?: string;\n    [key: string]: any;\n  };\n}) {\n  console.log('Email delivered successfully:', params);\n  \n  // Add your custom logic here:\n  // - Update delivery status in database\n  // - Track successful deliveries\n  // - Update user statistics\n  // - Trigger follow-up actions\n  // - Send delivery confirmations\n  // - Update email campaigns\n  \n  // Example: Log successful delivery\n  const recipients = Array.isArray(params.to) ? params.to.join(', ') : params.to;\n  console.log(`Email delivered to: ${recipients} - Message ID: ${params.messageId}`);\n  \n  // Example: Track analytics\n  // await trackEvent('email_delivered', {\n  //   messageId: params.messageId,\n  //   recipients: Array.isArray(params.to) ? params.to.length : 1,\n  //   subject: params.subject,\n  //   mailerType: params.metadata?.mailerType,\n  //   deliveryTime: params.timestamp,\n  // });\n  \n  // Example: Update database\n  // await updateEmailStatus(params.messageId, 'delivered', params.timestamp);\n  \n  // Example: Trigger follow-up actions\n  // if (params.subject.includes('Welcome')) {\n  //   await scheduleFollowUpEmail(params.to, 'onboarding_day_2', 24 * 60 * 60 * 1000);\n  // }\n  \n  // Example: Update user statistics\n  // await incrementUserEmailsSent(params.metadata?.from);\n  \n  return { success: true };\n}\n\n/**\n * Called when an email fails to send\n */\nexport async function onEmailFailed(params: {\n  to: string | string[];\n  subject: string;\n  error: string;\n  timestamp: Date;\n  metadata?: {\n    from?: string;\n    mailerType?: string;\n    [key: string]: any;\n  };\n}) {\n  console.log('Email sending failed:', params);\n  \n  // Add your custom logic here:\n  // - Log errors for debugging\n  // - Track failure rates\n  // - Alert administrators\n  // - Retry logic\n  // - Update user notifications\n  // - Fallback to alternative mailer\n  \n  // Example: Log the failure\n  const recipients = Array.isArray(params.to) ? params.to.join(', ') : params.to;\n  console.error(`Email failed to send to: ${recipients} - Error: ${params.error}`);\n  \n  // Example: Track analytics\n  // await trackEvent('email_failed', {\n  //   recipients: Array.isArray(params.to) ? params.to.length : 1,\n  //   subject: params.subject,\n  //   error: params.error,\n  //   mailerType: params.metadata?.mailerType,\n  //   timestamp: params.timestamp,\n  // });\n  \n  // Example: Alert administrators for critical emails\n  // if (params.subject.includes('Critical') || params.subject.includes('Alert')) {\n  //   await sendSlackAlert(`Critical email failed: ${params.error}`);\n  // }\n  \n  // Example: Retry logic\n  // if (params.error.includes('rate limit') || params.error.includes('temporary')) {\n  //   await scheduleEmailRetry(params.to, params.subject, 5 * 60 * 1000); // Retry in 5 minutes\n  // }\n  \n  // Example: Update failure statistics\n  // await incrementEmailFailures(params.metadata?.from, params.error);\n  \n  // Example: Notify user of failure (for important emails)\n  // if (params.metadata?.notifyOnFailure) {\n  //   await notifyUserOfEmailFailure(params.metadata.from, params.error);\n  // }\n  \n  return { success: true };\n}\n\n/**\n * Called when an email bounces (if webhook is configured)\n */\nexport async function onEmailBounced(params: {\n  to: string;\n  messageId?: string;\n  bounceType: 'hard' | 'soft';\n  reason: string;\n  timestamp: Date;\n  metadata?: {\n    [key: string]: any;\n  };\n}) {\n  console.log('Email bounced:', params);\n  \n  // Add your custom logic here:\n  // - Update email status\n  // - Mark email as invalid (for hard bounces)\n  // - Track bounce rates\n  // - Clean up mailing lists\n  // - Alert administrators\n  \n  // Example: Log the bounce\n  console.log(`Email bounced: ${params.to} - Type: ${params.bounceType} - Reason: ${params.reason}`);\n  \n  // Example: Handle hard bounces\n  // if (params.bounceType === 'hard') {\n  //   await markEmailAsInvalid(params.to);\n  //   await removeFromMailingLists(params.to);\n  // }\n  \n  // Example: Track analytics\n  // await trackEvent('email_bounced', {\n  //   email: params.to,\n  //   bounceType: params.bounceType,\n  //   reason: params.reason,\n  //   messageId: params.messageId,\n  //   timestamp: params.timestamp,\n  // });\n  \n  return { success: true };\n}\n\n/**\n * Called when a recipient complains about spam (if webhook is configured)\n */\nexport async function onEmailComplaint(params: {\n  to: string;\n  messageId?: string;\n  timestamp: Date;\n  metadata?: {\n    [key: string]: any;\n  };\n}) {\n  console.log('Email complaint received:', params);\n  \n  // Add your custom logic here:\n  // - Remove from mailing lists\n  // - Track complaint rates\n  // - Alert administrators\n  // - Review email content\n  // - Update sender reputation\n  \n  // Example: Log the complaint\n  console.log(`Spam complaint from: ${params.to} - Message ID: ${params.messageId}`);\n  \n  // Example: Immediate actions\n  // await removeFromAllMailingLists(params.to);\n  // await markAsComplainer(params.to);\n  \n  // Example: Track analytics\n  // await trackEvent('email_complaint', {\n  //   email: params.to,\n  //   messageId: params.messageId,\n  //   timestamp: params.timestamp,\n  // });\n  \n  // Example: Alert administrators\n  // await sendSlackAlert(`Spam complaint received from ${params.to}`);\n  \n  return { success: true };\n}\n", "type": "registry:lib"}]}